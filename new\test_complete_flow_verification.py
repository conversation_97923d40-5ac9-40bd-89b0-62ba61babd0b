#!/usr/bin/env python3
"""
完整流程验证测试
验证整个analyse -> MCP工具链 -> 文献检索流程是否正常工作
"""

import asyncio
import sys
import os
import json

# 添加项目根目录到路径
project_root = os.path.join(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.search.analyse import AnalyseInterface

async def test_complete_flow():
    """测试完整的分析流程"""
    print("🚀 开始完整流程验证测试...")
    
    # 初始化analyse模块
    analyse = AnalyseInterface()
    
    # 设置测试参数
    topic = "人工智能"
    interaction_rounds = 1  # 减少交互轮次以便快速测试
    
    print(f"📝 测试主题: {topic}")
    print(f"🔄 交互轮次: {interaction_rounds}")
    
    try:
        # 运行完整的分析流程
        print("\n🔍 开始执行分析流程...")
        result = await analyse.analyse(topic, description=None, top_n=20)
        
        print("\n✅ 分析流程完成!")
        print(f"📊 结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print("📋 结果内容:")
            for key, value in result.items():
                if key == "literature_papers" and isinstance(value, list):
                    print(f"  📚 {key}: {len(value)} 篇文献")
                    for i, paper in enumerate(value[:3]):  # 只显示前3篇
                        print(f"    {i+1}. {paper.get('title', 'N/A')}")
                        print(f"       URL: {paper.get('url', 'N/A')}")
                elif key == "search_memory" and isinstance(value, list):
                    print(f"  🧠 {key}: {len(value)} 条记忆")
                elif key == "conversation_history" and isinstance(value, list):
                    print(f"  💬 {key}: {len(value)} 条对话历史")
                else:
                    print(f"  📄 {key}: {str(value)[:100]}...")
        else:
            print(f"📄 结果: {str(result)[:200]}...")
            
        # 检查关键指标
        if isinstance(result, dict):
            literature_papers = result.get("literature_papers", [])
            conversation_history = result.get("conversation_history", [])
            
            print(f"\n📈 关键指标:")
            print(f"  📚 文献数量: {len(literature_papers)}")
            print(f"  💬 对话历史长度: {len(conversation_history)}")
            
            # 验证是否成功获取文献
            if len(literature_papers) > 0:
                print("✅ 成功获取文献!")
                return True
            else:
                print("❌ 未获取到文献")
                return False
        else:
            print("❌ 返回结果格式不正确")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 完整流程验证测试")
    print("=" * 60)
    
    success = await test_complete_flow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功! 整个流程正常工作")
    else:
        print("💥 测试失败! 需要进一步调试")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
