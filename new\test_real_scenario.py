#!/usr/bin/env python3
"""
真实场景测试：多模态推理主题的完整analyse工作流
"""

import asyncio
import sys
import os
import logging
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from search.analyse import AnalyseInterface

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_real_multimodal_reasoning_analysis():
    """测试多模态推理主题的真实分析"""
    print("🔍 开始多模态推理主题的真实分析测试...")
    print("=" * 60)
    
    # 测试参数
    topic = "多模态推理"
    description = "研究多模态推理在人工智能中的应用，包括视觉-语言模型、跨模态理解和推理机制"
    top_n = 10  # 减少数量以节省API调用
    
    print(f"📝 测试主题: {topic}")
    print(f"📄 详细描述: {description}")
    print(f"🎯 目标文献数量: {top_n}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    try:
        # 创建分析接口
        analyser = AnalyseInterface(max_interaction_rounds=1)  # 减少交互轮次
        print("✅ AnalyseInterface 创建成功")
        
        # 准备refined_topic（模拟主题细化结果）
        refined_topic = {
            'original_topic': topic,
            'core_concept': topic,
            'description': description,
            'refined': True,
            'keywords': ['多模态', '推理', '视觉语言模型', '跨模态理解', 'AI'],
            'research_focus': '多模态推理技术在AI系统中的实现和应用'
        }
        
        print("📋 主题细化完成:")
        print(f"  核心概念: {refined_topic['core_concept']}")
        print(f"  研究焦点: {refined_topic['research_focus']}")
        print(f"  关键词: {', '.join(refined_topic['keywords'])}")
        print("-" * 60)
        
        # 执行文献检索
        print("🚀 开始执行文献检索工作流...")
        start_time = datetime.now()
        
        literature_results = await analyser._retrieve_literature(
            refined_topic=refined_topic,
            top_n=top_n
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⏱️ 检索完成，耗时: {duration:.2f} 秒")
        print("-" * 60)
        
        # 分析结果
        print("📊 分析检索结果:")
        print(f"📚 获得文献数量: {len(literature_results)}")
        
        if literature_results:
            print("\n📋 文献结果详情:")
            for i, result in enumerate(literature_results[:5], 1):  # 显示前5个结果
                print(f"\n{i}. 文献信息:")
                if isinstance(result, dict):
                    # 显示结果的关键信息
                    for key, value in result.items():
                        if key in ['title', 'url', 'abstract', 'content', 'type', 'relevance_score']:
                            if isinstance(value, str) and len(value) > 200:
                                print(f"   {key}: {value[:200]}...")
                            else:
                                print(f"   {key}: {value}")
                else:
                    print(f"   内容: {str(result)[:200]}...")
            
            if len(literature_results) > 5:
                print(f"\n... 还有 {len(literature_results) - 5} 个结果未显示")
        
        # 检查搜索记忆
        print(f"\n🧠 搜索记忆记录: {len(analyser.search_memory)} 条")
        if analyser.search_memory:
            print("最近的工具调用:")
            for i, memory in enumerate(analyser.search_memory[-3:], 1):  # 显示最后3条记录
                print(f"  {i}. 工具: {memory.get('tool_used', 'Unknown')}")
                print(f"     任务: {memory.get('task', 'Unknown')[:100]}...")
                print(f"     原因: {memory.get('reasoning', 'Unknown')[:100]}...")
        
        print("\n" + "=" * 60)
        print("🎉 真实场景测试完成！")
        print(f"✅ 成功检索到 {len(literature_results)} 篇相关文献")
        print(f"⏱️ 总耗时: {duration:.2f} 秒")
        
        return literature_results
        
    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_mcp_tools_individually():
    """单独测试每个MCP工具的功能"""
    print("\n🛠️ 单独测试MCP工具功能...")
    print("=" * 60)
    
    try:
        analyser = AnalyseInterface()
        
        # 测试数据
        test_topic = "多模态推理"
        test_description = "多模态推理在AI中的应用研究"
        
        async def test_tools_task(session):
            tools_response = await session.list_tools()
            available_tools = [tool.model_dump() for tool in tools_response.tools]
            
            results = {}
            
            # 测试每个工具
            for tool in available_tools:
                tool_name = tool['name']
                print(f"\n🔧 测试工具: {tool_name}")
                
                try:
                    # 根据工具类型准备不同的参数
                    if tool_name == "generate_search_queries":
                        arguments = {
                            "topic": test_topic,
                            "description": test_description
                        }
                    elif tool_name == "web_search":
                        arguments = {
                            "queries": ["multimodal reasoning AI", "vision language models"],
                            "topic": test_topic,
                            "top_n": 5
                        }
                    elif tool_name == "analyze_search_results":
                        arguments = {
                            "urls": ["https://example.com/paper1", "https://example.com/paper2"],
                            "topic": test_topic,
                            "max_results": 3
                        }
                    elif tool_name == "crawl_urls":
                        arguments = {
                            "topic": test_topic,
                            "url_list": ["https://arxiv.org/abs/2301.00001"],
                            "top_n": 3
                        }
                    else:
                        print(f"  ⚠️ 未知工具类型: {tool_name}")
                        continue
                    
                    print(f"  📝 调用参数: {arguments}")
                    
                    # 调用工具
                    result = await session.call_tool(tool_name, arguments)
                    
                    # 处理结果
                    if hasattr(result, 'content') and result.content:
                        content = result.content[0].text if hasattr(result.content[0], 'text') else str(result.content[0])
                        print(f"  ✅ 工具调用成功")
                        print(f"  📄 结果长度: {len(content)} 字符")
                        print(f"  📋 结果预览: {content[:200]}...")
                        results[tool_name] = content
                    else:
                        print(f"  ⚠️ 工具返回空结果")
                        results[tool_name] = None
                        
                except Exception as tool_error:
                    print(f"  ❌ 工具调用失败: {tool_error}")
                    results[tool_name] = f"Error: {tool_error}"
            
            return results
        
        tool_results = await analyser._execute_with_mcp_session(test_tools_task)
        
        print("\n📊 工具测试结果汇总:")
        for tool_name, result in tool_results.items():
            status = "✅ 成功" if result and not str(result).startswith("Error:") else "❌ 失败"
            print(f"  {tool_name}: {status}")
        
        return tool_results
        
    except Exception as e:
        print(f"❌ MCP工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主测试函数"""
    print("🚀 开始多模态推理主题的真实场景测试")
    print("🔗 这将调用真实的API进行完整的文献检索工作流")
    print("⚠️ 注意：此测试将消耗API配额")
    print("=" * 80)
    
    # 确认是否继续
    print("是否继续执行真实API调用测试？")
    print("输入 'yes' 继续，其他任何输入将取消测试")
    
    # 在脚本中自动确认（实际使用时可以取消注释下面的代码来要求用户确认）
    # user_input = input("请输入: ").strip().lower()
    # if user_input != 'yes':
    #     print("❌ 测试已取消")
    #     return
    
    print("✅ 自动确认，开始执行测试...")
    
    try:
        # 执行真实场景测试
        literature_results = await test_real_multimodal_reasoning_analysis()
        
        if literature_results is not None:
            print("\n🎊 真实场景测试成功完成！")
            
            # 可选：执行单独的工具测试
            print("\n是否执行单独的MCP工具测试？")
            # tool_results = await test_mcp_tools_individually()
            
            return 0
        else:
            print("\n❌ 真实场景测试失败")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
