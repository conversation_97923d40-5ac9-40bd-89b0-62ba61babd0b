#!/usr/bin/env python3
"""
测试MCP Server的工具列表和工具调用
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.search.llm_search_mcp_client import create_mcp_client_from_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_tools():
    """测试MCP工具列表和调用"""
    print("🧪 测试MCP工具列表和调用")
    print("="*40)
    
    try:
        # 创建MCP客户端
        print("📋 创建MCP客户端...")
        client = await create_mcp_client_from_config()
        print("✅ MCP客户端创建成功")
        
        # 获取工具列表
        print("🔧 获取工具列表...")
        tools = await client.list_tools()
        print(f"✅ 获取到 {len(tools)} 个工具:")
        for tool in tools:
            if isinstance(tool, dict):
                print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
            else:
                print(f"  - {tool.name}: {tool.description}")

        # 检查generate_search_queries工具是否存在
        generate_tool = None
        for tool in tools:
            tool_name = tool.get('name') if isinstance(tool, dict) else tool.name
            if tool_name == "generate_search_queries":
                generate_tool = tool
                break

        if generate_tool:
            print(f"✅ 找到generate_search_queries工具")
            if isinstance(generate_tool, dict):
                print(f"   描述: {generate_tool.get('description', 'No description')}")
                print(f"   输入模式: {generate_tool.get('inputSchema', 'No schema')}")
            else:
                print(f"   描述: {generate_tool.description}")
                print(f"   输入模式: {generate_tool.inputSchema}")
        else:
            print("❌ 未找到generate_search_queries工具")
            return False
        
        # 测试工具调用（使用超时）
        print("🔧 测试generate_search_queries工具调用...")
        tool_args = {
            "topic": "machine learning basics",
            "description": "搜索机器学习基础知识",
            "model": "default"
        }
        
        print(f"工具参数: {tool_args}")
        print("⏳ 执行工具调用（30秒超时）...")
        
        try:
            # 设置30秒超时
            result = await asyncio.wait_for(
                client.call_tool("generate_search_queries", tool_args),
                timeout=30.0
            )
            print("✅ 工具调用成功!")
            
            print(f"📊 结果类型: {type(result)}")
            if isinstance(result, dict):
                print(f"📊 结果键: {list(result.keys())}")
                if "queries" in result:
                    print(f"📊 生成的查询数量: {len(result['queries'])}")
                    print(f"📊 查询示例: {result['queries'][:3]}")
            
        except asyncio.TimeoutError:
            print("❌ 工具调用超时（30秒）")
            return False
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 断开连接
        await client.disconnect()
        print("✅ 客户端断开连接")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 MCP工具测试")
    print("目标：验证MCP Server工具列表和工具调用")
    print()
    
    success = await test_mcp_tools()
    
    if success:
        print("\n🎉 MCP工具测试通过!")
        print("✅ 工具列表正常")
        print("✅ 工具调用正常")
    else:
        print("\n❌ MCP工具测试失败!")
        print("❌ 工具列表或调用有问题")

if __name__ == "__main__":
    asyncio.run(main())
