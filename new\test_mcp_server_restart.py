#!/usr/bin/env python3
"""
测试MCP服务器重启和配置重新加载
"""

import os
import sys
import json
import asyncio
import subprocess
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    config_path = project_root / "config" / "environment_config.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            env_config = json.load(f)
        
        # 设置OpenAI配置
        openai_config = env_config.get("api_keys", {}).get("openai", {})
        if openai_config.get("api_key"):
            os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
            print(f"  ✅ OPENAI_API_KEY 已设置")
        if openai_config.get("base_url"):
            os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
            os.environ["OPENAI_API_BASE"] = openai_config["base_url"]  # 兼容性
            print(f"  ✅ OPENAI_BASE_URL 已设置")
        
        # 设置搜索引擎配置
        search_engines = env_config.get("api_keys", {}).get("search_engines", {})
        if search_engines.get("serpapi_key"):
            os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
            print(f"  ✅ SERPAPI_KEY 已设置: {search_engines['serpapi_key'][:10]}...")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 环境设置失败: {e}")
        return False

async def test_mcp_tools():
    """测试MCP工具"""
    print("\n🔧 测试MCP工具...")
    
    try:
        from mcp import ClientSession, StdioServerParameters
        from mcp.client.stdio import stdio_client
        
        # MCP服务器参数
        server_params = StdioServerParameters(
            command="python",
            args=[str(project_root / "src" / "search" / "llm_search_mcp_server.py")],
            env=dict(os.environ)
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化
                await session.initialize()
                
                # 列出工具
                tools = await session.list_tools()
                print(f"  📋 可用工具: {[tool.name for tool in tools.tools]}")
                
                # 测试generate_search_queries工具
                print(f"\n  🔍 测试generate_search_queries工具...")
                result = await session.call_tool(
                    "generate_search_queries",
                    arguments={"topic": "Machine Learning Basics"}
                )
                
                print(f"  📝 查询生成结果:")
                for content in result.content:
                    if hasattr(content, 'text'):
                        print(f"     {content.text[:200]}...")
                
                return True
                
    except Exception as e:
        print(f"  ❌ MCP工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 MCP服务器重启和配置测试")
    print("=" * 50)
    
    # 设置环境
    if not setup_environment():
        print("❌ 环境设置失败")
        return
    
    # 测试MCP工具
    print("\n📡 测试MCP服务器...")
    success = asyncio.run(test_mcp_tools())
    
    if success:
        print("\n✅ MCP服务器测试成功！")
    else:
        print("\n❌ MCP服务器测试失败")

if __name__ == "__main__":
    main()
