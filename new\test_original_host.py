#!/usr/bin/env python3
"""
测试原版Host的工具调用，对比是否有同样问题
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.search.llm_search_host import LLMSearchHostV3

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_original_host():
    """测试原版Host的工具调用"""
    print("🧪 测试原版Host工具调用")
    print("="*40)
    
    try:
        # 创建原版Host
        print("📋 创建原版Host...")
        host = LLMSearchHostV3()
        print("✅ 原版Host创建成功")
        
        # 测试单个工具调用
        print("🔧 测试generate_search_queries工具...")
        tool_args = {
            "topic": "machine learning basics",
            "description": "搜索机器学习基础知识",
            "model": "default"
        }
        
        print(f"工具参数: {tool_args}")
        
        # 执行工具调用
        print("⏳ 执行工具调用...")
        result = await host._execute_tool("generate_search_queries", tool_args)
        print("✅ 工具调用成功!")
        
        print(f"📊 结果类型: {type(result)}")
        if isinstance(result, dict):
            print(f"📊 结果键: {list(result.keys())}")
            if "queries" in result:
                print(f"📊 生成的查询数量: {len(result['queries'])}")
                print(f"📊 查询示例: {result['queries'][:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 原版Host工具调用测试")
    print("目标：验证原版Host是否也有同样的卡死问题")
    print()
    
    success = await test_original_host()
    
    if success:
        print("\n🎉 原版Host测试通过!")
        print("✅ 原版Host工具调用正常")
    else:
        print("\n❌ 原版Host测试失败!")
        print("❌ 原版Host也有工具调用问题")

if __name__ == "__main__":
    asyncio.run(main())
