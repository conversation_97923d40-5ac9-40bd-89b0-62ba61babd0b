#!/usr/bin/env python3
"""
测试MCP工具的输出内容，确保工具间能够正确衔接
"""

import asyncio
import sys
import os
import logging
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_tool_chain():
    """测试工具链的输出和衔接"""
    print("🔧 开始MCP工具链测试")
    print("=" * 80)
    
    try:
        # 从环境配置文件读取API密钥
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            env_config = json.load(f)
        
        # 构建环境变量
        env_vars = {"PYTHONPATH": "."}
        
        # 添加API密钥
        openai_config = env_config.get("api_keys", {}).get("openai", {})
        if openai_config.get("api_key"):
            env_vars["OPENAI_API_KEY"] = openai_config["api_key"]
        if openai_config.get("base_url"):
            env_vars["OPENAI_BASE_URL"] = openai_config["base_url"]
        
        search_engines = env_config.get("api_keys", {}).get("search_engines", {})
        if search_engines.get("serpapi_key"):
            env_vars["SERPAPI_KEY"] = search_engines["serpapi_key"]
        
        # MCP服务器参数
        mcp_server_params = StdioServerParameters(
            command="python",
            args=["-m", "src.search.llm_search_mcp_server"],
            env=env_vars
        )
        
        print("🔗 开始MCP会话...")
        
        async with stdio_client(mcp_server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ MCP会话初始化成功")
                
                # 测试主题
                topic = "机器学习"
                description = "机器学习算法和应用研究"
                
                print(f"\n📝 测试主题: {topic}")
                print(f"📄 描述: {description}")
                print("=" * 80)
                
                # Step 1: 生成搜索查询
                print("\n🔍 Step 1: 生成搜索查询")
                print("-" * 40)
                
                step1_args = {
                    "topic": topic,
                    "description": description
                }
                
                print(f"📥 输入参数: {step1_args}")
                
                try:
                    result1 = await asyncio.wait_for(
                        session.call_tool("generate_search_queries", step1_args),
                        timeout=60.0
                    )
                    
                    print(f"✅ 工具调用成功")
                    print(f"📊 结果类型: {type(result1)}")
                    
                    if hasattr(result1, 'content') and result1.content:
                        content1 = result1.content[0].text
                        print(f"📄 返回内容长度: {len(content1)} 字符")
                        print(f"📝 返回内容预览:")
                        print(content1[:500] + "..." if len(content1) > 500 else content1)
                        
                        # 解析JSON结果
                        try:
                            queries_data = json.loads(content1)
                            queries = queries_data.get("queries", [])
                            print(f"🔍 生成的查询数量: {len(queries)}")
                            for i, query in enumerate(queries[:3], 1):
                                print(f"  {i}. {query}")
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析失败: {e}")
                            queries = ["机器学习", "machine learning"]
                    else:
                        print("❌ 无返回内容")
                        queries = ["机器学习", "machine learning"]
                        
                except Exception as e:
                    print(f"❌ Step 1 失败: {e}")
                    queries = ["机器学习", "machine learning"]
                
                # Step 2: 网络搜索
                print(f"\n🌐 Step 2: 网络搜索")
                print("-" * 40)
                
                step2_args = {
                    "queries": queries[:3],  # 只用前3个查询
                    "topic": topic,
                    "top_n": 5,
                    "engine": "google"
                }
                
                print(f"📥 输入参数: {step2_args}")
                
                try:
                    result2 = await asyncio.wait_for(
                        session.call_tool("web_search", step2_args),
                        timeout=90.0
                    )
                    
                    print(f"✅ 工具调用成功")
                    print(f"📊 结果类型: {type(result2)}")
                    
                    if hasattr(result2, 'content') and result2.content:
                        content2 = result2.content[0].text
                        print(f"📄 返回内容长度: {len(content2)} 字符")
                        print(f"📝 返回内容预览:")
                        print(content2[:800] + "..." if len(content2) > 800 else content2)
                        
                        # 解析JSON结果
                        try:
                            search_data = json.loads(content2)
                            urls = search_data.get("urls", [])
                            print(f"🔗 获得的URL数量: {len(urls)}")
                            for i, url in enumerate(urls[:3], 1):
                                print(f"  {i}. {url}")
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析失败: {e}")
                            urls = []
                    else:
                        print("❌ 无返回内容")
                        urls = []
                        
                except Exception as e:
                    print(f"❌ Step 2 失败: {e}")
                    urls = []
                
                # Step 3: 分析搜索结果
                print(f"\n📊 Step 3: 分析搜索结果")
                print("-" * 40)
                
                step3_args = {
                    "urls": urls[:5],  # 只用前5个URL
                    "topic": topic,
                    "max_results": 3
                }
                
                print(f"📥 输入参数: {step3_args}")
                
                try:
                    result3 = await asyncio.wait_for(
                        session.call_tool("analyze_search_results", step3_args),
                        timeout=60.0
                    )
                    
                    print(f"✅ 工具调用成功")
                    print(f"📊 结果类型: {type(result3)}")
                    
                    if hasattr(result3, 'content') and result3.content:
                        content3 = result3.content[0].text
                        print(f"📄 返回内容长度: {len(content3)} 字符")
                        print(f"📝 返回内容:")
                        print(content3)
                        
                        # 解析JSON结果
                        try:
                            analysis_data = json.loads(content3)
                            analyzed_urls = analysis_data.get("analyzed_urls", [])
                            print(f"🎯 分析后的URL数量: {len(analyzed_urls)}")
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析失败: {e}")
                            analyzed_urls = urls[:3]
                    else:
                        print("❌ 无返回内容")
                        analyzed_urls = urls[:3]
                        
                except Exception as e:
                    print(f"❌ Step 3 失败: {e}")
                    analyzed_urls = urls[:3]
                
                # Step 4: 爬取URL内容（如果有URL的话）
                if analyzed_urls:
                    print(f"\n🕷️ Step 4: 爬取URL内容")
                    print("-" * 40)
                    
                    step4_args = {
                        "topic": topic,
                        "url_list": analyzed_urls[:2],  # 只爬取前2个URL
                        "top_n": 2
                    }
                    
                    print(f"📥 输入参数: {step4_args}")
                    
                    try:
                        result4 = await asyncio.wait_for(
                            session.call_tool("crawl_urls", step4_args),
                            timeout=360.0
                        )
                        
                        print(f"✅ 工具调用成功")
                        print(f"📊 结果类型: {type(result4)}")
                        
                        if hasattr(result4, 'content') and result4.content:
                            content4 = result4.content[0].text
                            print(f"📄 返回内容长度: {len(content4)} 字符")
                            print(f"📝 返回内容预览:")
                            print(content4[:1000] + "..." if len(content4) > 1000 else content4)
                            
                            # 解析JSON结果
                            try:
                                crawl_data = json.loads(content4)
                                results = crawl_data.get("results", [])
                                print(f"📚 爬取到的文档数量: {len(results)}")
                                for i, result in enumerate(results[:2], 1):
                                    title = result.get("title", "无标题")
                                    content_len = len(result.get("content", ""))
                                    print(f"  {i}. {title} ({content_len} 字符)")
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON解析失败: {e}")
                        else:
                            print("❌ 无返回内容")
                            
                    except Exception as e:
                        print(f"❌ Step 4 失败: {e}")
                else:
                    print(f"\n⚠️ 跳过Step 4: 没有可爬取的URL")
                
                print("\n" + "=" * 80)
                print("🎉 工具链测试完成！")
                return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print(f"🚀 开始MCP工具链输出测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        success = await test_tool_chain()
        
        if success:
            print("\n✅ 工具链测试通过!")
            return 0
        else:
            print("\n❌ 工具链测试失败!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
