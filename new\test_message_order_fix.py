#!/usr/bin/env python3
"""
测试修复后的消息顺序问题
"""

import os
import sys
import json
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    config_path = project_root / "config" / "environment_config.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            env_config = json.load(f)
        
        # 设置OpenAI配置
        openai_config = env_config.get("api_keys", {}).get("openai", {})
        if openai_config.get("api_key"):
            os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
            print(f"  ✅ OPENAI_API_KEY 已设置")
        if openai_config.get("base_url"):
            os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
            os.environ["OPENAI_API_BASE"] = openai_config["base_url"]  # 兼容性
            print(f"  ✅ OPENAI_BASE_URL 已设置")
        
        # 设置搜索引擎配置
        search_engines = env_config.get("api_keys", {}).get("search_engines", {})
        if search_engines.get("serpapi_key"):
            os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
            print(f"  ✅ SERPAPI_KEY 已设置: {search_engines['serpapi_key'][:10]}...")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 环境设置失败: {e}")
        return False

async def test_analyse_quick():
    """快速测试analyse流程"""
    print("\n🔍 快速测试analyse流程...")
    
    try:
        from search.analyse import AnalyseInterface
        
        # 创建analyse实例
        print("  📦 创建AnalyseInterface实例...")
        analyse = AnalyseInterface(
            base_dir=str(project_root),
            max_interaction_rounds=1,  # 只进行1轮交互
            llm_model="gpt-3.5-turbo"
        )
        print("  ✅ AnalyseInterface实例创建成功")
        
        # 模拟用户输入（自动继续）
        class MockInput:
            def __call__(self, prompt):
                print(f"    自动回复: 继续")
                return "继续"
        
        # 替换input函数
        import builtins
        original_input = builtins.input
        builtins.input = MockInput()
        
        try:
            # 运行analyse，限制结果数量以加快测试
            print("  🚀 开始analyse流程...")
            results = await analyse.analyse(
                topic="Machine Learning Basics",
                top_n=5  # 只获取5个结果以加快测试
            )
            
            print(f"\n  📊 Analyse结果:")
            print(f"     总文献数量: {len(results)}")
            
            if results:
                print(f"     成功获取文献结果！")
                for i, result in enumerate(results[:2]):  # 只显示前2个
                    title = result.get('title', 'N/A')[:50]
                    url = result.get('url', 'N/A')[:50]
                    print(f"       {i+1}. {title}... - {url}...")
                return True
            else:
                print(f"     ⚠️ 没有获取到文献结果")
                return False
            
        finally:
            # 恢复原始input函数
            builtins.input = original_input
        
    except Exception as e:
        print(f"  ❌ Analyse流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 消息顺序修复测试")
    print("=" * 50)
    
    # 设置环境
    if not setup_environment():
        print("❌ 环境设置失败")
        return
    
    # 测试analyse流程
    success = asyncio.run(test_analyse_quick())
    
    if success:
        print("\n✅ 消息顺序修复成功！")
        print("🎉 Analyse模块现在可以正常运行")
    else:
        print("\n❌ 仍有问题需要修复")

if __name__ == "__main__":
    main()
