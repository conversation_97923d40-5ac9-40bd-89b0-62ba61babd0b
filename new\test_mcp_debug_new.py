#!/usr/bin/env python3
"""
调试MCP服务器的详细日志输出
"""

import asyncio
import json
import logging
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        env_config = json.load(f)
    
    # 设置API密钥
    search_engines = env_config.get("api_keys", {}).get("search_engines", {})
    if search_engines.get("serpapi_key"):
        os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
        print(f"  ✅ SERPAPI_KEY 已设置: {search_engines['serpapi_key'][:10]}...")
    
    # 设置OpenAI配置
    openai_config = env_config.get("api_keys", {}).get("openai", {})
    if openai_config.get("api_key"):
        os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
        print(f"  ✅ OPENAI_API_KEY 已设置")
    if openai_config.get("base_url"):
        os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
        print(f"  ✅ OPENAI_BASE_URL 已设置")

async def test_mcp_with_logging():
    """测试MCP并捕获详细日志"""
    print("\n🔌 测试MCP客户端连接...")
    
    try:
        from mcp import ClientSession, StdioServerParameters
        from mcp.client.stdio import stdio_client
        
        # 启动MCP客户端
        server_params = StdioServerParameters(
            command="python",
            args=[os.path.join(os.path.dirname(__file__), "src", "search", "llm_search_mcp_server.py")],
            env=dict(os.environ)  # 传递当前环境变量
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("  ✅ MCP客户端连接成功")
                
                # 初始化
                await session.initialize()
                print("  ✅ MCP会话初始化成功")
                
                # 测试简单的web_search
                print(f"\n🌐 测试简单web_search...")
                try:
                    result = await session.call_tool(
                        "web_search",
                        arguments={
                            "queries": ["机器学习"],
                            "topic": "人工智能",
                            "top_n": 2
                        }
                    )
                    
                    print(f"  ✅ web_search调用成功")
                    
                    if result.content and len(result.content) > 0:
                        content_text = result.content[0].text
                        print(f"  📝 完整返回内容:")
                        print(content_text)
                        
                        # 解析JSON
                        try:
                            result_data = json.loads(content_text)
                            urls = result_data.get("urls", [])
                            print(f"\n  🎯 解析结果:")
                            print(f"     URL数量: {len(urls)}")
                            print(f"     查询: {result_data.get('queries', [])}")
                            print(f"     引擎: {result_data.get('engine', 'unknown')}")
                            
                            if urls:
                                print(f"     获取的URL:")
                                for i, url in enumerate(urls):
                                    print(f"       {i+1}. {url}")
                                return True  # 成功获取URL
                            else:
                                print(f"     ⚠️ 未获取到任何URL")
                                return False  # 没有获取到URL
                                
                        except json.JSONDecodeError as je:
                            print(f"  ❌ JSON解析失败: {je}")
                            return False
                    else:
                        print(f"  ❌ 返回内容为空")
                        return False
                        
                except Exception as tool_error:
                    print(f"  ❌ web_search调用失败: {tool_error}")
                    import traceback
                    traceback.print_exc()
                    return False
                
    except Exception as e:
        print(f"❌ MCP测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始MCP服务器调试测试")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    # 测试MCP连接
    success = asyncio.run(test_mcp_with_logging())
    
    if success:
        print("\n✅ 调试测试完成 - 成功获取URL")
        return 0
    else:
        print("\n❌ 调试测试失败 - 未获取到URL")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
