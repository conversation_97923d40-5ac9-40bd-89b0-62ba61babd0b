#!/usr/bin/env python3
"""
直接测试LLM_search类的batch_web_search方法
"""

import os
import sys
import json
import logging

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 从配置文件读取
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            env_config = json.load(f)
        
        # 设置OpenAI配置
        openai_config = env_config.get("api_keys", {}).get("openai", {})
        if openai_config.get("api_key"):
            os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
            print(f"  ✅ OPENAI_API_KEY 已设置")
        if openai_config.get("base_url"):
            os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
            print(f"  ✅ OPENAI_BASE_URL 已设置")
        
        # 设置搜索引擎配置
        search_engines = env_config.get("api_keys", {}).get("search_engines", {})
        if search_engines.get("serpapi_key"):
            os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
            print(f"  ✅ SERPAPI_KEY 已设置: {search_engines['serpapi_key'][:10]}...")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 环境设置失败: {e}")
        return False

def test_llm_search_direct():
    """直接测试LLM_search"""
    print("\n🔍 直接测试LLM_search...")
    
    try:
        from search.LLM_search import LLM_search
        
        # 创建实例
        print("  📦 创建LLM_search实例...")
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI",
            engine="google",
            each_query_result=3
        )
        print("  ✅ LLM_search实例创建成功")
        
        # 检查API密钥
        print(f"  🔑 API密钥检查:")
        print(f"     serpapi_key: {'已设置' if llm_search.serpapi_key else '未设置'}")
        print(f"     bing_key: {'已设置' if llm_search.bing_subscription_key else '未设置'}")
        
        if llm_search.serpapi_key:
            print(f"     serpapi_key值: {llm_search.serpapi_key[:10]}...")
        
        # 测试单个web_search
        print(f"\n  🌐 测试单个web_search...")
        try:
            search_result = llm_search.web_search("机器学习")
            print(f"  ✅ 单个web_search成功")
            print(f"     结果类型: {type(search_result)}")
            print(f"     结果长度: {len(search_result) if isinstance(search_result, dict) else 'N/A'}")
            
            if isinstance(search_result, dict):
                print(f"     前3个键: {list(search_result.keys())[:3]}")
            
        except Exception as e:
            print(f"  ❌ 单个web_search失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试batch_web_search
        print(f"\n  📦 测试batch_web_search...")
        try:
            queries = ["机器学习", "深度学习"]
            topic = "人工智能"
            
            batch_result = llm_search.batch_web_search(queries, topic, top_n=5)
            print(f"  ✅ batch_web_search成功")
            print(f"     结果类型: {type(batch_result)}")
            print(f"     结果长度: {len(batch_result) if isinstance(batch_result, list) else 'N/A'}")
            
            if isinstance(batch_result, list):
                print(f"     前3个URL: {batch_result[:3]}")
            
        except Exception as e:
            print(f"  ❌ batch_web_search失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"  ❌ LLM_search测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始LLM_search直接测试")
    print("=" * 60)
    
    # 设置环境
    if not setup_environment():
        print("❌ 环境设置失败，退出测试")
        return 1
    
    # 测试LLM_search
    if not test_llm_search_direct():
        print("❌ LLM_search测试失败")
        return 1
    
    print("\n✅ 所有测试通过!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
