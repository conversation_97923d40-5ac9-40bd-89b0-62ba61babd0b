#!/usr/bin/env python3
"""
调试web_search工具的API密钥问题
"""

import asyncio
import sys
import os
import logging
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_web_search_debug():
    """调试web_search工具"""
    print("🔧 开始web_search调试测试")
    print("=" * 80)
    
    try:
        # 从环境配置文件读取API密钥
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            env_config = json.load(f)
        
        # 构建环境变量
        env_vars = {"PYTHONPATH": "."}
        
        # 添加API密钥
        openai_config = env_config.get("api_keys", {}).get("openai", {})
        if openai_config.get("api_key"):
            env_vars["OPENAI_API_KEY"] = openai_config["api_key"]
        if openai_config.get("base_url"):
            env_vars["OPENAI_BASE_URL"] = openai_config["base_url"]
        
        search_engines = env_config.get("api_keys", {}).get("search_engines", {})
        if search_engines.get("serpapi_key"):
            env_vars["SERPAPI_KEY"] = search_engines["serpapi_key"]
            print(f"✅ 客户端设置 SERPAPI_KEY: {search_engines['serpapi_key'][:10]}...")
        
        print(f"🔧 环境变量: {list(env_vars.keys())}")
        
        # MCP服务器参数
        mcp_server_params = StdioServerParameters(
            command="python",
            args=["-m", "src.search.llm_search_mcp_server"],
            env=env_vars
        )
        
        print("🔗 开始MCP会话...")
        
        async with stdio_client(mcp_server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ MCP会话初始化成功")
                
                # 测试web_search工具
                print(f"\n🌐 测试web_search工具")
                print("-" * 40)
                
                args = {
                    "queries": ["机器学习"],
                    "topic": "机器学习",
                    "top_n": 3,
                    "engine": "google"
                }
                
                print(f"📥 输入参数: {args}")

                # 先测试一个简单的工具调用，看看是否能触发调试信息
                print(f"\n🔍 先测试generate_search_queries工具...")
                try:
                    simple_result = await asyncio.wait_for(
                        session.call_tool("generate_search_queries", {
                            "topic": "测试",
                            "description": "简单测试"
                        }),
                        timeout=30.0
                    )
                    print(f"✅ generate_search_queries调用成功")
                except Exception as e:
                    print(f"❌ generate_search_queries调用失败: {e}")

                print(f"\n🌐 现在测试web_search工具...")
                try:
                    result = await asyncio.wait_for(
                        session.call_tool("web_search", args),
                        timeout=90.0
                    )
                    
                    print(f"✅ 工具调用成功")
                    print(f"📊 结果类型: {type(result)}")
                    
                    if hasattr(result, 'content') and result.content:
                        content = result.content[0].text
                        print(f"📄 返回内容长度: {len(content)} 字符")
                        print(f"📝 完整返回内容:")
                        print(content)
                        
                        # 解析JSON结果
                        try:
                            data = json.loads(content)
                            urls = data.get("urls", [])
                            error = data.get("processing_metadata", {}).get("error", "")
                            
                            print(f"\n📊 解析结果:")
                            print(f"   URL数量: {len(urls)}")
                            print(f"   错误信息: {error}")
                            
                            if urls:
                                print(f"   前3个URL:")
                                for i, url in enumerate(urls[:3], 1):
                                    print(f"     {i}. {url}")
                            else:
                                print(f"   ❌ 没有获得URL，可能的原因:")
                                print(f"      - API密钥问题")
                                print(f"      - 搜索引擎配置问题")
                                print(f"      - 网络连接问题")
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析失败: {e}")
                    else:
                        print("❌ 无返回内容")
                        
                except Exception as e:
                    print(f"❌ 工具调用失败: {e}")
                    import traceback
                    traceback.print_exc()
                
                print("\n" + "=" * 80)
                print("🎉 web_search调试测试完成！")
                return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print(f"🚀 开始web_search调试测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        success = await test_web_search_debug()
        
        if success:
            print("\n✅ 调试测试通过!")
            return 0
        else:
            print("\n❌ 调试测试失败!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
