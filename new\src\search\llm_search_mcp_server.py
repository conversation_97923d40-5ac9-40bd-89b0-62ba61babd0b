import asyncio
import json
import logging
import sys
import os
import time
import re
from typing import Dict, Any, List

from mcp.server import Server
from mcp.types import Resource, Tool, TextContent
import mcp.server.stdio

try:
    from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
    CRAWL4AI_AVAILABLE = True
except ImportError:
    CRAWL4AI_AVAILABLE = False
    AsyncWebCrawler = None
    CacheMode = None
    CrawlerRunConfig = None

try:
    from LLM_search import LLM_search
except ImportError:
    try:
        import sys
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'LLMxMapReduce_V2', 'src'))
        from LLM_search import LLM_search
    except ImportError:
        LLM_search = None

try:
    from exceptions import AnalyseError
except ImportError:
    class AnalyseError(Exception):
        pass

try:
    from request import RequestWrapper
except ImportError:
    try:
        import sys
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'LLMxMapReduce_V2'))
        from request import RequestWrapper
    except ImportError:
        RequestWrapper = None

# 配置日志输出到文件和控制台
import logging.handlers

# 创建日志目录
log_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'logs')
os.makedirs(log_dir, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'mcp_server.log'), encoding='utf-8'),
        logging.StreamHandler(sys.stderr)  # 输出到stderr以便客户端能看到
    ]
)

logger = logging.getLogger(__name__)

app = Server("llm-search-server")

def load_server_config():
    """从统一环境配置文件加载Server配置"""
    # 加载环境配置
    env_config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'environment_config.json')

    try:
        # 读取环境配置
        with open(env_config_path, 'r', encoding='utf-8') as f:
            env_config = json.load(f)

        # 设置环境变量
        api_keys = env_config.get("api_keys", {})

        # 设置OpenAI API配置
        openai_config = api_keys.get("openai", {})
        if openai_config.get("api_key"):
            os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
        if openai_config.get("base_url"):
            os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]

        # 设置搜索引擎API密钥
        search_engines = api_keys.get("search_engines", {})
        if search_engines.get("serpapi_key"):
            os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
            logger.info(f"✅ SERPAPI_KEY 已设置: {search_engines['serpapi_key'][:10]}...")
        else:
            logger.warning("❌ SERPAPI_KEY 未在配置文件中找到")

        if search_engines.get("bing_subscription_key"):
            os.environ["BING_SEARCH_V7_SUBSCRIPTION_KEY"] = search_engines["bing_subscription_key"]
            logger.info(f"✅ BING_SEARCH_V7_SUBSCRIPTION_KEY 已设置")
        else:
            logger.warning("❌ BING_SEARCH_V7_SUBSCRIPTION_KEY 未在配置文件中找到")

        # 验证环境变量是否正确设置
        serpapi_key = os.environ.get("SERPAPI_KEY")
        bing_key = os.environ.get("BING_SEARCH_V7_SUBSCRIPTION_KEY")
        logger.info(f"🔍 环境变量验证 - SERPAPI_KEY: {'✅ 已设置' if serpapi_key else '❌ 未设置'}")
        logger.info(f"🔍 环境变量验证 - BING_KEY: {'✅ 已设置' if bing_key else '❌ 未设置'}")

        # 构建服务器配置
        models = env_config.get("models", {})
        search_settings = env_config.get("search_settings", {})
        timeout_settings = env_config.get("timeout_settings", {})

        server_config = {
            # 模型配置
            "default_model": models.get("default_model", "gemini-2.0-flash-thinking-exp-01-21"),
            "default_infer_type": models.get("default_infer_type", "OpenAI"),
            "content_analysis_model": models.get("content_analysis_model", "gemini-2.0-flash-thinking-exp-01-21"),
            "similarity_model": models.get("similarity_model", "gemini-2.0-flash-thinking-exp-01-21"),
            "page_refine_model": models.get("page_refine_model", "gemini-2.0-flash-thinking-exp-01-21"),

            # 搜索配置
            "default_engine": search_settings.get("default_engine", "google"),
            "default_each_query_result": search_settings.get("default_each_query_result", 10),
            "default_top_n": search_settings.get("default_top_n", 20),
            "default_similarity_threshold": search_settings.get("default_similarity_threshold", 80),
            "default_min_length": search_settings.get("default_min_length", 350),
            "default_max_length": search_settings.get("default_max_length", 20000),

            # 超时配置
            "llm_request_timeout": timeout_settings.get("llm_request_timeout", 30),
            "web_search_timeout": timeout_settings.get("web_search_timeout", 60),
            "crawling_timeout": timeout_settings.get("crawling_timeout", 300),
            "single_url_crawl_timeout": timeout_settings.get("single_url_crawl_timeout", 60),
            "content_analysis_timeout": timeout_settings.get("content_analysis_timeout", 120),
            "similarity_scoring_timeout": timeout_settings.get("similarity_scoring_timeout", 90)
        }

        logger.info(f"Environment config loaded successfully")
        logger.info(f"Server config: {server_config}")
        return server_config

    except Exception as e:
        logger.warning(f"Failed to load environment config, using defaults: {e}")
        return {
            "default_model": "gemini-2.0-flash-thinking-exp-01-21",
            "default_infer_type": "OpenAI",
            "content_analysis_model": "gemini-2.0-flash-thinking-exp-01-21",
            "similarity_model": "gemini-2.0-flash-thinking-exp-01-21",
            "page_refine_model": "gemini-2.0-flash-thinking-exp-01-21",
            "default_engine": "google",
            "default_each_query_result": 10,
            "default_top_n": 20,
            "default_similarity_threshold": 80,
            "default_min_length": 350,
            "default_max_length": 20000,
            "llm_request_timeout": 30,
            "web_search_timeout": 60,
            "crawling_timeout": 300,
            "single_url_crawl_timeout": 60,
            "content_analysis_timeout": 120,
            "similarity_scoring_timeout": 90
        }

SERVER_CONFIG = load_server_config()
llm_search_instances = {}

# 辅助函数：用于生成兼容 LLMxMapReduce_V2 和 Survey 格式的 JSON
def proc_title_to_str(origin_title: str) -> str:
    """
    将标题转换为bibkey格式
    复制自 LLMxMapReduce_V2/src/utils/process_str.py
    """
    if not origin_title:
        return ""

    title = origin_title.lower().strip()
    title = title.replace("-", "_")
    title = re.sub(r'[^\w\s\_]', '', title)
    title = title.replace(" ", "_")
    title = re.sub(r'_{2,}', '_', title)
    return title

def estimate_tokens(text: str) -> int:
    """
    估算文本的token数量
    使用简单的启发式方法：单词数 * 1.3
    """
    if not text:
        return 0
    words = text.split()
    return int(len(words) * 1.3)

def extract_abstract(text: str, max_length: int = 500) -> str:
    """
    从文本中提取摘要
    简单实现：取前max_length个字符
    """
    if not text:
        return ""

    # 去除多余的空白字符
    cleaned_text = re.sub(r'\s+', ' ', text.strip())

    if len(cleaned_text) <= max_length:
        return cleaned_text

    # 尝试在句号处截断，避免截断句子
    truncated = cleaned_text[:max_length]
    last_period = truncated.rfind('.')

    if last_period > max_length * 0.7:  # 如果句号位置合理
        return truncated[:last_period + 1]
    else:
        return truncated + "..."
@app.list_resources()
async def list_resources() -> List[Resource]:
    return [
        Resource(
            uri="llm://search/prompts",
            name="LLM Search Prompts",
            description="LLM搜索相关的提示词模板",
            mimeType="application/json"
        )
    ]

@app.read_resource()
async def read_resource(uri: str) -> str:
    if uri == "llm://search/prompts":
        prompts = {
            "query_generation": """
你是一个专业的搜索查询优化专家。请为给定的研究主题生成多个高质量的搜索查询。

## 输入信息
主题: {topic}
描述: {description}

## 查询生成原则
1. **多角度覆盖**: 从不同角度和层面生成查询
2. **关键词优化**: 使用精准的学术和专业术语
3. **查询多样性**: 包含不同类型的查询（概念性、具体性、比较性等）
4. **搜索引擎友好**: 优化查询以获得最佳搜索结果

## 输出格式
请按以下格式输出查询列表：
```json
{"查询1";"查询2";"查询3";"查询4";"查询5"}
```

请生成5-8个高质量的搜索查询。
""",
            "relevance_analysis": """
你是一个搜索结果相关性分析专家。请分析给定URL列表与研究主题的相关性。

## 分析任务
主题: {topic}
URL列表: {urls}

## 分析维度
1. **内容相关性**: URL内容与主题的匹配程度
2. **权威性**: 来源的可信度和专业性
3. **时效性**: 信息的新鲜度和时效性
4. **深度**: 内容的详细程度和深度

## 输出要求
请为每个URL提供相关性评分（0-100），并按相关性排序。
""",
            "page_refine": """
分析并处理以下与'{topic}'相关的网页内容。输出主体文本，去除图片链接，网址链接，广告，无意义重复字符等。禁止对内容进行总结，应保留所有与主题相关的信息。

原始网页内容：
{raw_content}

[输出要求]
- 标题：<TITLE>你的标题</TITLE>
- 过滤后文本：<CONTENT>过滤后文本</CONTENT>
""",
            "similarity_scoring": """
请你依据下列主题和在互联网上检索到的内容，判断这段内容的质量分数。

主题：{topic}
检索到的内容：{content}

请你依据以下几个维度，对这段检索到的内容进行打分。请你尽可能严格，批判性给分。

1. 内容与主题的相关程度。这需要考虑内容是否能被视为主题的一部分子内容进行展开。
2. 内容能够用于撰写与主题的文本的质量。这需要考虑文本的长度（例如：如果长度非常短，则用于参考的价值相对较低）、文本中是否包含较多乱码、文本本身的质量等。

请你综合考量上述两个维度，先给出评分的理由，再进行评分。你需要对每一个维度进行评分，评分范围是0-100。0表示完全不相关，100表示完全相关。完成每一个维度的评分后，你需要进行计算，得出最后的平均分。

注意，评分需要用<SCORE></SCORE>包裹起来。例如<SCORE>78</SCORE>

回答示例：
理由：...
相似度评分：<SCORE>89</SCORE>
"""
        }
        return json.dumps(prompts, ensure_ascii=False, indent=2)
    else:
        raise ValueError(f"Unknown resource: {uri}")

@app.list_tools()
async def list_tools() -> List[Tool]:
    return [
        Tool(
            name="generate_search_queries",
            description="基于LLM生成优化的搜索查询",
            inputSchema={
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "研究主题"
                    },
                    "description": {
                        "type": "string",
                        "description": "主题的可选描述或上下文"
                    },
                    "model": {
                        "type": "string",
                        "description": "用于查询生成的LLM模型",
                        "default": "gemini-2.0-flash-thinking-exp-01-21"
                    }
                },
                "required": ["topic"]
            }
        ),
        Tool(
            name="web_search",
            description="使用提供的查询执行网络搜索",
            inputSchema={
                "type": "object",
                "properties": {
                    "queries": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "要执行的搜索查询列表"
                    },
                    "topic": {
                        "type": "string",
                        "description": "用于相关性过滤的主要主题"
                    },
                    "top_n": {
                        "type": "integer",
                        "description": "返回的最相关URL数量",
                        "default": 20
                    },
                    "engine": {
                        "type": "string",
                        "description": "使用的搜索引擎 (google, bing, baidu)",
                        "default": "google"
                    }
                },
                "required": ["queries", "topic"]
            }
        ),
        Tool(
            name="analyze_search_results",
            description="分析和过滤搜索结果的相关性",
            inputSchema={
                "type": "object",
                "properties": {
                    "urls": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "要分析的URL列表"
                    },
                    "topic": {
                        "type": "string",
                        "description": "用于分析相关性的主题"
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "返回的最大结果数量",
                        "default": 10
                    }
                },
                "required": ["urls", "topic"]
            }
        ),
        Tool(
            name="crawl_urls",
            description="异步爬取URL列表并处理内容：爬取 -> 内容过滤 -> 相似度评分 -> 结果排序",
            inputSchema={
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "研究主题，用于内容过滤和相似度评分"
                    },
                    "url_list": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "要爬取的URL列表"
                    },
                    "top_n": {
                        "type": "integer",
                        "description": "返回的最高质量结果数量",
                        "default": 80
                    },
                    "model": {
                        "type": "string",
                        "description": "用于内容处理和相似度评分的LLM模型",
                        "default": "gemini-2.0-flash-thinking-exp-01-21"
                    },
                    "similarity_threshold": {
                        "type": "number",
                        "description": "相似度阈值 (0-100)",
                        "default": 80
                    },
                    "min_length": {
                        "type": "integer",
                        "description": "内容最小长度",
                        "default": 350
                    },
                    "max_length": {
                        "type": "integer",
                        "description": "内容最大长度",
                        "default": 20000
                    }
                },
                "required": ["topic", "url_list"]
            }
        )
    ]
@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    global llm_search_instances

    try:
        if name == "generate_search_queries":
            result = await _generate_search_queries(
                arguments["topic"],
                arguments.get("description", ""),
                arguments.get("model")  
            )
        elif name == "web_search":
            result = await _web_search(
                arguments["queries"],
                arguments["topic"],
                arguments.get("top_n"),
                arguments.get("engine") 
            )
        elif name == "analyze_search_results":
            result = await _analyze_search_results(
                arguments["urls"],
                arguments["topic"],
                arguments.get("max_results", 10) 
            )
        elif name == "crawl_urls":
            result = await _crawl_urls(
                arguments["topic"],
                arguments["url_list"],
                arguments.get("top_n"), 
                arguments.get("model"), 
                arguments.get("similarity_threshold"), 
                arguments.get("min_length"), 
                arguments.get("max_length")
            )
        else:
            raise ValueError(f"Unknown tool: {name}")

        return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]

    except Exception as e:
        logger.error(f"Error calling tool {name}: {e}")
        return [TextContent(type="text", text=f"Error: {str(e)}")]

def _get_llm_search_instance(model: str = None, engine: str = None):
    """获取LLM搜索实例，使用配置文件中的默认值"""
    global llm_search_instances

    # 使用配置文件中的默认值
    if model is None:
        model = SERVER_CONFIG["default_model"]
    if engine is None:
        engine = SERVER_CONFIG["default_engine"]

    infer_type = SERVER_CONFIG["default_infer_type"]
    each_query_result = SERVER_CONFIG["default_each_query_result"]

    key = f"{model}_{engine}_{infer_type}"
    if key not in llm_search_instances:
        # 确保环境变量已设置（重新加载配置以防万一）
        import os

        # 重新确认环境变量设置
        serpapi_key = os.environ.get("SERPAPI_KEY")
        bing_key = os.environ.get("BING_SEARCH_V7_SUBSCRIPTION_KEY")

        logger.info(f"🔑 创建LLM_search实例前的环境变量检查:")
        logger.info(f"   SERPAPI_KEY: {'已设置' if serpapi_key else '未设置'}")
        if serpapi_key:
            logger.info(f"   SERPAPI_KEY值: {serpapi_key[:10]}...")
        logger.info(f"   BING_SEARCH_V7_SUBSCRIPTION_KEY: {'已设置' if bing_key else '未设置'}")

        # 如果环境变量未设置，尝试重新从配置文件加载
        if not serpapi_key and not bing_key:
            logger.warning("⚠️ 环境变量未设置，尝试重新从配置文件加载...")
            try:
                env_config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'environment_config.json')
                with open(env_config_path, 'r', encoding='utf-8') as f:
                    env_config = json.load(f)

                search_engines = env_config.get("api_keys", {}).get("search_engines", {})
                if search_engines.get("serpapi_key"):
                    os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
                    logger.info(f"🔄 重新设置SERPAPI_KEY: {search_engines['serpapi_key'][:10]}...")

                if search_engines.get("bing_subscription_key"):
                    os.environ["BING_SEARCH_V7_SUBSCRIPTION_KEY"] = search_engines["bing_subscription_key"]
                    logger.info(f"🔄 重新设置BING_SEARCH_V7_SUBSCRIPTION_KEY")

            except Exception as reload_e:
                logger.error(f"❌ 重新加载配置失败: {reload_e}")

        try:
            logger.info(f"🚀 开始创建LLM_search实例: {key}")
            llm_search_instances[key] = LLM_search(
                model=model,
                infer_type=infer_type,
                engine=engine,
                each_query_result=each_query_result
            )
            logger.info(f"✅ LLM_search实例创建成功: {key}")
        except Exception as e:
            logger.error(f"❌ LLM_search实例创建失败: {e}")
            logger.error(f"   模型: {model}, 推理类型: {infer_type}, 引擎: {engine}")
            # 输出更详细的环境变量信息用于调试
            logger.error(f"   当前SERPAPI_KEY: {os.environ.get('SERPAPI_KEY', 'None')}")
            logger.error(f"   当前BING_KEY: {os.environ.get('BING_SEARCH_V7_SUBSCRIPTION_KEY', 'None')}")
            raise e
    else:
        logger.info(f"♻️ 复用已存在的LLM_search实例: {key}")

    return llm_search_instances[key]
async def _generate_search_queries(topic: str, description: str = "", model: str = None) -> Dict[str, Any]:
    logger.info(f"Generating search queries for topic: {topic}")

    try:
        if model is None:
            model = SERVER_CONFIG["default_model"]

        infer_type = SERVER_CONFIG["default_infer_type"]

        llm_search = _get_llm_search_instance(model=model)

        # 在线程池中执行同步的get_queries方法
        import asyncio
        import functools
        loop = asyncio.get_event_loop()

        # 使用functools.partial来传递关键字参数
        get_queries_func = functools.partial(
            llm_search.get_queries,
            topic=topic,
            description=description
        )

        # 添加超时机制，防止LLM调用卡住
        try:
            queries = await asyncio.wait_for(
                loop.run_in_executor(None, get_queries_func),
                timeout=SERVER_CONFIG.get("llm_request_timeout", 25)  # 使用配置的超时时间
            )
        except asyncio.TimeoutError:
            logger.error(f"LLM query generation timed out for topic: {topic}")
            # 返回默认查询
            queries = [
                topic,
                f"{topic} research",
                f"{topic} analysis",
                f"{topic} study"
            ]

        result = {
            "topic": topic,
            "description": description,
            "model": model,
            "queries": queries,
            "query_count": len(queries),
            "processing_metadata": {
                "model": model,
                "method": "llm_generation",
                "timestamp": "2025-01-23"
            }
        }

        return result

    except Exception as e:
        logger.error(f"Error generating queries: {e}")
        default_queries = [
            topic,
            f"{topic} research",
            f"{topic} analysis",
            f"{topic} study",
            f"{topic} overview"
        ]

        return {
            "topic": topic,
            "description": description,
            "model": model or SERVER_CONFIG["default_model"],
            "queries": default_queries,
            "query_count": len(default_queries),
            "processing_metadata": {
                "model": model or SERVER_CONFIG["default_model"],
                "method": "fallback_generation",
                "error": str(e)
            }
        }
async def _web_search(queries: List[str], topic: str, top_n: int = None, engine: str = None) -> Dict[str, Any]:
    logger.info(f"Performing web search for {len(queries)} queries")

    try:
        if top_n is None:
            top_n = SERVER_CONFIG["default_top_n"]
        if engine is None:
            engine = SERVER_CONFIG["default_engine"]

        llm_search = _get_llm_search_instance(engine=engine)

        # 使用线程执行batch_web_search以避免事件循环冲突，添加超时机制
        import concurrent.futures
        import asyncio

        loop = asyncio.get_event_loop()

        try:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                urls = await asyncio.wait_for(
                    loop.run_in_executor(
                        executor,
                        llm_search.batch_web_search,
                        queries,
                        topic,
                        top_n
                    ),
                    timeout=SERVER_CONFIG.get("web_search_timeout", 60)  # 使用配置的超时时间
                )
        except asyncio.TimeoutError:
            logger.error(f"Web search timed out for queries: {queries}")
            # 返回空的URL列表作为fallback
            urls = []

        result = {
            "topic": topic,
            "queries": queries,
            "engine": engine,
            "urls": urls,
            "url_count": len(urls),
            "top_n": top_n,
            "processing_metadata": {
                "engine": engine,
                "query_count": len(queries),
                "result_count": len(urls),
                "method": "batch_web_search"
            }
        }

        return result

    except Exception as e:
        logger.error(f"Error in web search: {e}")
        return {
            "topic": topic,
            "queries": queries,
            "engine": engine or SERVER_CONFIG["default_engine"],
            "urls": [],
            "url_count": 0,
            "top_n": top_n or SERVER_CONFIG["default_top_n"],
            "processing_metadata": {
                "engine": engine or SERVER_CONFIG["default_engine"],
                "query_count": len(queries),
                "result_count": 0,
                "method": "fallback",
                "error": str(e)
            }
        }
async def _analyze_search_results(urls: List[str], topic: str, max_results: int) -> Dict[str, Any]:
    logger.info(f"Analyzing {len(urls)} URLs for relevance to topic: {topic}")

    try:
        # 这里可以实现更复杂的分析逻辑
        # 目前简单返回前max_results个URL
        analyzed_urls = urls[:max_results]

        # 计算一些基本的分析指标
        analysis_scores = []
        for i, url in enumerate(analyzed_urls):
            # 简单的相关性评分
            score = max(0.5, 1.0 - (i * 0.1)) 
            analysis_scores.append({
                "url": url,
                "relevance_score": score,
                "rank": i + 1
            })

        result = {
            "topic": topic,
            "original_count": len(urls),
            "analyzed_urls": analyzed_urls,
            "final_count": len(analyzed_urls),
            "analysis_method": "ranking_based_truncation",
            "analysis_scores": analysis_scores,
            "processing_metadata": {
                "input_count": len(urls),
                "output_count": len(analyzed_urls),
                "max_results": max_results,
                "analysis_method": "simple_ranking"
            }
        }

        return result

    except Exception as e:
        logger.error(f"Error analyzing search results: {e}")
        return {
            "topic": topic,
            "original_count": len(urls),
            "analyzed_urls": [],
            "final_count": 0,
            "analysis_method": "error_fallback",
            "analysis_scores": [],
            "processing_metadata": {
                "input_count": len(urls),
                "output_count": 0,
                "max_results": max_results,
                "error": str(e)
            }
        }
async def _crawl_urls(topic: str, url_list: List[str], top_n: int = None, model: str = None,
                     similarity_threshold: float = None, min_length: int = None, max_length: int = None) -> Dict[str, Any]:
    logger.info(f"Starting crawling process for {len(url_list)} URLs with topic: {topic}")

    try:
        if top_n is None:
            top_n = SERVER_CONFIG["default_top_n"]
        if model is None:
            model = SERVER_CONFIG["default_model"]
        if similarity_threshold is None:
            similarity_threshold = SERVER_CONFIG["default_similarity_threshold"]
        if min_length is None:
            min_length = SERVER_CONFIG["default_min_length"]
        if max_length is None:
            max_length = SERVER_CONFIG["default_max_length"]

        if not CRAWL4AI_AVAILABLE:
            raise ImportError("crawl4ai is not available")
        if RequestWrapper is None:
            raise ImportError("RequestWrapper is not available")

        import time
        import re

        # 使用配置中的模型设置
        content_model = SERVER_CONFIG.get("content_analysis_model", model)
        infer_type = SERVER_CONFIG.get("default_infer_type", "OpenAI")
        request_wrapper = RequestWrapper(model=content_model, infer_type=infer_type)

        process_start_time = time.time()
        stage_time = process_start_time

        # 爬取URL，添加超时机制
        try:
            crawl_results = await asyncio.wait_for(
                _crawl_urls_stage(topic, url_list),
                timeout=SERVER_CONFIG.get("crawling_timeout", 300)  # 使用配置的超时时间（5分钟）
            )
            logger.info(f"Stage 1 - Crawling completed in {time.time() - stage_time:.2f} seconds, with {len(crawl_results)} results")
        except asyncio.TimeoutError:
            logger.error("URL crawling stage timed out")
            crawl_results = []
        stage_time = time.time()

        # 内容过滤和标题生成
        if crawl_results:
            try:
                filtered_results = await asyncio.wait_for(
                    _process_filter_and_titles_stage(crawl_results, request_wrapper),
                    timeout=SERVER_CONFIG.get("content_analysis_timeout", 120)  # 使用配置的超时时间
                )
                logger.info(f"Stage 2 - Content filtering completed in {time.time() - stage_time:.2f} seconds, with {len(filtered_results)} results")
            except asyncio.TimeoutError:
                logger.error("Content filtering stage timed out")
                filtered_results = crawl_results  # 使用原始结果
        else:
            filtered_results = []
        stage_time = time.time()

        # 相似度评分
        if filtered_results:
            try:
                scored_results = await asyncio.wait_for(
                    _process_similarity_scores_stage(filtered_results, request_wrapper),
                    timeout=SERVER_CONFIG.get("similarity_scoring_timeout", 90)  # 使用配置的超时时间
                )
                logger.info(f"Stage 3 - Similarity scoring completed in {time.time() - stage_time:.2f} seconds, with {len(scored_results)} results")
            except asyncio.TimeoutError:
                logger.error("Similarity scoring stage timed out")
                scored_results = filtered_results  # 使用过滤后的结果
        else:
            scored_results = []
        stage_time = time.time()

        # 结果处理和排序
        final_results = _process_and_sort_results(
            scored_results, top_n, similarity_threshold, min_length, max_length
        )
        logger.info(f"Stage 4 - Result processing completed in {time.time() - stage_time:.2f} seconds")

        total_time = time.time() - process_start_time
        logger.info(f"Total crawling process completed in {total_time:.2f} seconds")

        # 生成兼容 LLMxMapReduce_V2 JSONL 格式的结果
        # 主要结果：主题+论文列表的格式
        main_result = {
            "title": topic,
            "papers": final_results
        }

        # 详细的处理信息（用于调试和监控）
        detailed_result = {
            "topic": topic,
            "total_urls": len(url_list),
            "crawl_results": len(crawl_results),
            "filtered_results": len(filtered_results),
            "scored_results": len(scored_results),
            "final_results": final_results,
            "final_count": len(final_results),
            "processing_metadata": {
                "model": model,
                "similarity_threshold": similarity_threshold,
                "min_length": min_length,
                "max_length": max_length,
                "top_n": top_n,
                "total_time": total_time,
                "success": True
            },
            # 添加兼容格式的主要结果
            "llm_mapreduce_format": main_result
        }

        return detailed_result

    except Exception as e:
        logger.error(f"Error in crawling pipeline: {e}")
        return {
            "topic": topic,
            "total_urls": len(url_list),
            "crawl_results": 0,
            "filtered_results": 0,
            "scored_results": 0,
            "final_results": [],
            "final_count": 0,
            "processing_metadata": {
                "model": model,
                "similarity_threshold": similarity_threshold,
                "min_length": min_length,
                "max_length": max_length,
                "top_n": top_n,
                "success": False,
                "error": str(e)
            }
        }

async def _crawl_urls_stage(topic: str, url_list: List[str]) -> List[Dict[str, Any]]:
    import asyncio

    if not CRAWL4AI_AVAILABLE:
        raise ImportError("crawl4ai is not available")

    MAX_CONCURRENT_CRAWLS = 10
    input_queue = asyncio.Queue()
    output_queue = asyncio.Queue()
    total_items = len(url_list)

    # 添加URL到队列
    for url in url_list:
        await input_queue.put((url, topic))

    async def consumer():
        while True:
            try:
                url, topic = input_queue.get_nowait()
                try:
                    result = await _crawl_single_url(url, topic)
                    await output_queue.put(result)
                    logger.info(f"URL crawling completed, remaining: {input_queue.qsize()}/{total_items}, URL: {url}")
                finally:
                    input_queue.task_done()
            except asyncio.QueueEmpty:
                break

    consumers = [asyncio.create_task(consumer()) for _ in range(MAX_CONCURRENT_CRAWLS)]

    await input_queue.join()

    for consumer_task in consumers:
        consumer_task.cancel()

    results = []
    while not output_queue.empty():
        results.append(await output_queue.get())

    return results

async def _crawl_single_url(url: str, topic: str) -> Dict[str, Any]:
    try:
        if not CRAWL4AI_AVAILABLE:
            raise ImportError("crawl4ai is not available")

        crawler_run_config = CrawlerRunConfig(
            page_timeout=SERVER_CONFIG.get("single_url_crawl_timeout", 60),
            cache_mode=CacheMode.BYPASS  # 使用配置的单个URL爬取超时时间
        )

        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(url=url, config=crawler_run_config)
            raw_markdown = result.markdown_v2.raw_markdown
            logger.info(f"Content length={len(raw_markdown)} for URL={url}")

            return {
                "topic": topic,
                "url": url,
                "raw_content": raw_markdown,
                "error": False,
            }
    except Exception as e:
        logger.error(f"Crawling failed for URL={url}: {e}")
        return {
            "topic": topic,
            "url": url,
            "raw_content": f"Error: Crawling failed({e})",
            "error": True,
        }

async def _process_filter_and_titles_stage(crawl_results: List[Dict[str, Any]], request_wrapper) -> List[Dict[str, Any]]:
    import asyncio

    MAX_CONCURRENT_PROCESSES = 10
    input_queue = asyncio.Queue()
    output_queue = asyncio.Queue()

    for data in crawl_results:
        if not data.get("error", False):
            await input_queue.put(data)

    async def processor():
        while True:
            try:
                data = input_queue.get_nowait()
                try:
                    result = await _process_filter_and_title_single(data, request_wrapper)
                    await output_queue.put(result)
                finally:
                    input_queue.task_done()
            except asyncio.QueueEmpty:
                break

    processors = [asyncio.create_task(processor()) for _ in range(MAX_CONCURRENT_PROCESSES)]

    await input_queue.join()

    for processor_task in processors:
        processor_task.cancel()

    results = []
    while not output_queue.empty():
        results.append(await output_queue.get())

    return results

async def _process_filter_and_title_single(data: Dict[str, Any], request_wrapper) -> Dict[str, Any]:
    import re

    try:
        prompt_template = await read_resource("llm://search/prompts")
        prompts = json.loads(prompt_template)

        prompt = prompts["page_refine"].format(
            topic=data["topic"], raw_content=data["raw_content"]
        )
        res = request_wrapper.completion(prompt)

        # 尝试提取标题和内容
        title = re.search(r"<TITLE>(.*?)</TITLE>", res, re.DOTALL)
        content = re.search(r"<CONTENT>(.*?)</CONTENT>", res, re.DOTALL)

        if not title or not content:
            # 如果格式不正确，尝试从原始内容中提取
            logger.warning(f"Invalid response format for URL={data.get('url', 'unknown')}, trying fallback extraction")

            # 尝试从原始内容中提取标题（通常是第一行或前几行）
            raw_content = data.get("raw_content", "")
            lines = raw_content.split('\n')

            # 寻找可能的标题
            potential_title = ""
            for line in lines[:10]:  # 检查前10行
                line = line.strip()
                if line and len(line) < 200:  # 标题通常较短
                    potential_title = line
                    break

            if not potential_title:
                potential_title = f"Content from {data.get('url', 'unknown')}"

            # 使用原始内容作为过滤后的内容（简单清理）
            filtered_content = raw_content
            # 简单清理：移除过多的空行和特殊字符
            filtered_content = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_content)
            filtered_content = re.sub(r'[^\w\s\u4e00-\u9fff.,;:!?()[\]{}"\'-]', ' ', filtered_content)

            data["title"] = potential_title[:200]  # 限制标题长度
            data["content"] = filtered_content[:10000]  # 限制内容长度
            data["filter_error"] = False

            logger.info(f"Used fallback extraction for URL={data.get('url', 'unknown')}")
        else:
            data["title"] = title.group(1).strip()
            data["content"] = content.group(1).strip()
            data["filter_error"] = False

        return data

    except Exception as e:
        logger.error(f"Content filtering failed for URL={data.get('url', 'unknown')}: {e}")
        data["title"] = f"Error processing: {data.get('url', 'unknown')}"
        data["content"] = f"Error: Content filtering failed({e})"
        data["filter_error"] = True
        return data

async def _process_similarity_scores_stage(filtered_results: List[Dict[str, Any]], request_wrapper) -> List[Dict[str, Any]]:

    import asyncio

    MAX_CONCURRENT_PROCESSES = 10
    input_queue = asyncio.Queue()
    output_queue = asyncio.Queue()

    for data in filtered_results:
        if not data.get("filter_error", False):
            await input_queue.put(data)

    async def scorer():
        while True:
            try:
                data = input_queue.get_nowait()
                try:
                    result = await _process_similarity_score_single(data, request_wrapper)
                    await output_queue.put(result)
                finally:
                    input_queue.task_done()
            except asyncio.QueueEmpty:
                break

    scorers = [asyncio.create_task(scorer()) for _ in range(MAX_CONCURRENT_PROCESSES)]

    await input_queue.join()

    for scorer_task in scorers:
        scorer_task.cancel()

    results = []
    while not output_queue.empty():
        results.append(await output_queue.get())

    return results

async def _process_similarity_score_single(data: Dict[str, Any], request_wrapper) -> Dict[str, Any]:
    import re

    try:
        prompt_template = await read_resource("llm://search/prompts")
        prompts = json.loads(prompt_template)
        
        prompt = prompts["similarity_scoring"].format(
            topic=data["topic"], content=data["content"]
        )
        res = request_wrapper.completion(prompt)
        score_match = re.search(r"<SCORE>(\d+)</SCORE>", res)

        if score_match:
            data["similarity_score"] = int(score_match.group(1))
        else:
            data["similarity_score"] = 50
            logger.warning(f"No score found in response for URL={data.get('url', 'unknown')}, using default score 50")

        data["score_error"] = False
        return data

    except Exception as e:
        logger.error(f"Similarity scoring failed for URL={data.get('url', 'unknown')}: {e}")
        data["similarity_score"] = 0
        data["score_error"] = True
        return data

def _process_and_sort_results(scored_results: List[Dict[str, Any]], top_n: int,
                             similarity_threshold: float, min_length: int, max_length: int) -> List[Dict[str, Any]]:

    filtered_results = []
    for result in scored_results:
        if (not result.get("score_error", False) and
            result.get("similarity_score", 0) >= similarity_threshold and
            min_length <= len(result.get("content", "")) <= max_length):
            filtered_results.append(result)

    filtered_results.sort(key=lambda x: x.get("similarity_score", 0), reverse=True)

    final_results = filtered_results[:top_n]

    # 生成兼容 LLMxMapReduce_V2 和 Survey 格式的结果
    formatted_results = []
    for i, result in enumerate(final_results):
        content = result.get("content", "")
        title = result.get("title", "")

        # 生成兼容格式的论文数据
        paper_data = {
            # LLMxMapReduce_V2 核心字段
            "title": title,
            "url": result.get("url", ""),
            "txt": content,  # content → txt
            "similarity": result.get("similarity_score", 0),  # similarity_score → similarity

            # Survey 兼容字段
            "bibkey": proc_title_to_str(title),
            "abstract": extract_abstract(content, 500),
            "txt_token": estimate_tokens(content),
            "txt_length": len(content),

            # 元数据字段
            "source_type": "web_crawl",
            "crawl_timestamp": time.time(),
            "processing_stage": "crawl_complete"
        }
        formatted_results.append(paper_data)

    return formatted_results

async def main():
    logger.info("Starting LLM Search MCP Server...")
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
