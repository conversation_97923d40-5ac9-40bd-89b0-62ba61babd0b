import os
import json
import logging
import re
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from mcp.client.session import ClientSession
from mcp.client.stdio import StdioServerParameters, stdio_client
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from request.wrapper import RequestWrapper
    
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AnalyseInterface:
    def __init__(self,
                 base_dir: str = "new/test",
                 max_interaction_rounds: int = 3,
                 config_path: Optional[str] = None,
                 llm_model: str = "gpt-3.5-turbo",
                 llm_infer_type: str = "OpenAI"):
 
        self.base_dir = Path(base_dir)
        self.max_interaction_rounds = max_interaction_rounds
        self.config_path = config_path
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.config_dir = self.base_dir / "config"
        self.config_dir.mkdir(exist_ok=True)
        self.llm_model = llm_model
        self.llm_infer_type = llm_infer_type

        # 初始化记忆系统
        self.search_memory = []  # 存储搜索过程的记忆
        self.conversation_history = []  # 存储完整的对话历史，包括工具调用

        # 清空conversation_history以避免之前的孤立tool消息
        self.conversation_history.clear()
        self._init_llm_components()
        self._init_llm_search()

        self._load_config()
        self._load_prompt_templates()

        logger.info(f"AnalyseInterface initialized:")
        logger.info(f"  - Base directory: {self.base_dir}")
        logger.info(f"  - Max interaction rounds: {self.max_interaction_rounds}")
        logger.info(f"  - LLM model: {self.llm_model}")
        logger.info(f"  - Search engine: MCP Client")

    def _init_llm_components(self):
        try:
            self.llm_wrapper = RequestWrapper(
                model=self.llm_model,
                infer_type=self.llm_infer_type,
                use_memory=True,
                max_context_messages=10
            )
            logger.info("LLM wrapper initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM wrapper: {e}")
            raise

    def _init_llm_search(self):
        """初始化MCP客户端配置"""
        try:
            # 从环境配置文件读取API密钥
            env_config = self._load_environment_config()

            # 构建环境变量字典
            env_vars = {
                "PYTHONPATH": ".",
            }

            # 添加OpenAI配置
            openai_config = env_config.get("api_keys", {}).get("openai", {})
            if openai_config.get("api_key"):
                env_vars["OPENAI_API_KEY"] = openai_config["api_key"]
            if openai_config.get("base_url"):
                env_vars["OPENAI_BASE_URL"] = openai_config["base_url"]

            # 添加搜索引擎配置
            search_engines = env_config.get("api_keys", {}).get("search_engines", {})
            if search_engines.get("serpapi_key"):
                env_vars["SERPAPI_KEY"] = search_engines["serpapi_key"]
            if search_engines.get("bing_subscription_key"):
                env_vars["BING_SEARCH_V7_SUBSCRIPTION_KEY"] = search_engines["bing_subscription_key"]

            # 配置MCP服务器参数
            self.mcp_server_params = StdioServerParameters(
                command="python",
                args=["-m", "src.search.llm_search_mcp_server"],
                env=env_vars
            )

            # 初始化对话记忆管理
            self.search_memory = []
            self.current_session = None

            logger.info("MCP client configuration initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MCP client configuration: {e}")
            raise

    def _load_environment_config(self):
        """加载环境配置文件"""
        import json
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'environment_config.json')

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load environment config: {e}")
            # 返回默认配置
            return {
                "api_keys": {
                    "openai": {
                        "api_key": "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146",
                        "base_url": "https://api.shubiaobiao.cn/v1"
                    },
                    "search_engines": {
                        "serpapi_key": "",
                        "bing_subscription_key": ""
                    }
                }
            }

    async def _create_mcp_session(self):
        """创建新的MCP客户端会话上下文管理器"""
        try:
            # 返回stdio_client上下文管理器，让调用者管理生命周期
            return stdio_client(self.mcp_server_params)
        except Exception as e:
            logger.error(f"Failed to create MCP session: {e}")
            raise

    async def _execute_with_mcp_session(self, task_func):
        """使用MCP会话执行任务"""
        try:
            async with stdio_client(self.mcp_server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    logger.debug("Created and initialized MCP client session")
                    return await task_func(session)
        except Exception as e:
            logger.error(f"Error in MCP session execution: {e}")
            raise

    async def _call_mcp_tool_with_llm(self, session: ClientSession, task_description: str,
                                      available_tools: List[Dict]) -> Dict[str, Any]:
        """使用LLM选择和调用MCP工具，基于对话历史进行工具选择"""
        try:
            # 构建对话消息
            messages = [
                {
                    "role": "system",
                    "content": f"""你是一个智能文献搜索助手，使用Model Context Protocol (MCP)工具来完成搜索任务。

可用工具：
{json.dumps(available_tools, indent=2, ensure_ascii=False)}

请根据任务描述和之前的对话历史选择最合适的工具，并提供正确的参数。

重要提示：
1. 如果之前的工具调用生成了查询列表，在web_search中应该使用这些查询
2. 如果之前的工具调用返回了URL列表，在crawl_urls中应该使用这些URL
3. 仔细查看对话历史中的工具调用结果，确保正确传递数据

返回格式必须是JSON：
{{
    "tool_name": "工具名称",
    "arguments": {{参数字典}},
    "reasoning": "选择理由"
}}

只返回JSON，不要其他内容。"""
                }
            ]

            # 只添加有效的对话历史（确保消息顺序正确）
            # 过滤掉孤立的tool消息，只保留完整的assistant+tool消息对
            valid_history = []
            i = 0
            while i < len(self.conversation_history):
                msg = self.conversation_history[i]
                if msg.get("role") == "assistant" and "tool_calls" in msg:
                    # 这是一个包含tool_calls的assistant消息
                    valid_history.append(msg)
                    # 查找对应的tool消息
                    j = i + 1
                    while j < len(self.conversation_history) and self.conversation_history[j].get("role") == "tool":
                        valid_history.append(self.conversation_history[j])
                        j += 1
                    i = j
                elif msg.get("role") == "user":
                    # 保留用户消息
                    valid_history.append(msg)
                    i += 1
                else:
                    # 跳过孤立的tool消息或其他无效消息
                    i += 1

            messages.extend(valid_history)

            # 添加当前任务作为最后一个用户消息
            messages.append({
                "role": "user",
                "content": f"任务: {task_description}"
            })

            # 调用LLM获取工具选择，使用对话历史
            response = await self.llm_wrapper.async_request(messages)

            # 解析LLM响应
            try:
                # 清理响应，移除可能的代码块标记
                cleaned_response = response.strip()

                # 如果响应被包装在代码块中，提取JSON内容
                if cleaned_response.startswith('```json'):
                    # 找到JSON开始和结束位置
                    start_idx = cleaned_response.find('{')
                    end_idx = cleaned_response.rfind('}') + 1
                    if start_idx != -1 and end_idx > start_idx:
                        cleaned_response = cleaned_response[start_idx:end_idx]
                elif cleaned_response.startswith('```'):
                    # 处理其他类型的代码块
                    lines = cleaned_response.split('\n')
                    # 移除第一行和最后一行的```标记
                    if len(lines) > 2:
                        cleaned_response = '\n'.join(lines[1:-1])

                tool_choice = json.loads(cleaned_response)
                tool_name = tool_choice["tool_name"]
                arguments = tool_choice["arguments"]
                reasoning = tool_choice.get("reasoning", "")

                logger.info(f"LLM选择工具: {tool_name}, 原因: {reasoning}")

                # 调用MCP工具，添加超时机制
                logger.info(f"开始调用MCP工具: {tool_name}")
                logger.info(f"工具参数: {arguments}")

                import asyncio
                try:
                    # 根据工具类型设置不同的超时时间
                    if tool_name == "web_search":
                        timeout = 90.0  # web搜索需要更长时间
                    elif tool_name == "crawl_urls":
                        timeout = 360.0  # 爬虫需要最长时间（6分钟）
                    else:
                        timeout = 60.0  # 其他工具默认60秒

                    result = await asyncio.wait_for(
                        session.call_tool(tool_name, arguments),
                        timeout=timeout
                    )
                    logger.info(f"MCP工具调用成功: {tool_name}")
                    logger.info(f"结果类型: {type(result)}")

                except asyncio.TimeoutError:
                    logger.error(f"MCP工具调用超时: {tool_name}")
                    raise TimeoutError(f"MCP工具 {tool_name} 调用超时（{timeout}秒）")
                except Exception as tool_error:
                    logger.error(f"MCP工具调用失败: {tool_name}, 错误: {tool_error}")
                    raise

                # 记录对话历史（保持向后兼容）
                self.search_memory.append({
                    "task": task_description,
                    "tool_used": tool_name,
                    "arguments": arguments,
                    "reasoning": reasoning,
                    "result": result
                })

                # 保存OpenAI格式的对话历史
                import uuid
                tool_call_id = f"call_{uuid.uuid4().hex[:8]}"

                # 添加助手消息（包含工具调用）
                assistant_message = {
                    "role": "assistant",
                    "content": f"我选择使用 {tool_name} 工具。{reasoning}",
                    "tool_calls": [{
                        "id": tool_call_id,
                        "type": "function",
                        "function": {
                            "name": tool_name,
                            "arguments": json.dumps(arguments, ensure_ascii=False)
                        }
                    }]
                }
                self.conversation_history.append(assistant_message)

                # 添加工具结果消息
                tool_result_content = ""
                if hasattr(result, 'content') and result.content:
                    try:
                        tool_result_content = result.content[0].text
                    except (AttributeError, IndexError):
                        tool_result_content = str(result)
                else:
                    tool_result_content = str(result)

                tool_message = {
                    "role": "tool",
                    "tool_call_id": tool_call_id,
                    "content": tool_result_content
                }
                self.conversation_history.append(tool_message)

                return {
                    "tool_name": tool_name,
                    "arguments": arguments,
                    "reasoning": reasoning,
                    "result": result
                }

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                logger.error(f"Original response: {response}")
                logger.error(f"Cleaned response: {cleaned_response}")
                raise ValueError(f"LLM返回了无效的JSON格式: {response}")

        except Exception as e:
            logger.error(f"Error in MCP tool call with LLM: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    def _format_available_tools(self, tools: List[Dict]) -> str:
        """格式化可用工具信息"""
        formatted = []
        for tool in tools:
            name = tool.get("name", "")
            description = tool.get("description", "")

            # 格式化输入参数
            input_schema = tool.get("inputSchema", {})
            properties = input_schema.get("properties", {})
            required = input_schema.get("required", [])

            params_info = []
            for prop, prop_info in properties.items():
                prop_type = prop_info.get("type", "string")
                prop_desc = prop_info.get("description", "")
                is_required = prop in required
                req_marker = " (必需)" if is_required else " (可选)"
                params_info.append(f"{prop}: {prop_type}{req_marker} - {prop_desc}")

            params_str = "; ".join(params_info) if params_info else "无参数"
            formatted.append(f"- {name}: {description}\n  参数: {params_str}")

        return "\n".join(formatted)

    async def analyse(self, topic: str, description: Optional[str] = None,
                      top_n: int = 20, **kwargs) -> List[Dict[str, Any]]:
        """
        执行完整的文献分析工作流

        Args:
            topic: 研究主题
            description: 主题描述（可选）
            top_n: 目标文献数量
            **kwargs: 其他参数

        Returns:
            List[Dict[str, Any]]: 文献检索结果列表（已在llm_search_server中保存）
        """
        logger.info(f"Starting comprehensive literature analysis for topic: '{topic}'")

        # 清空对话历史，确保每次分析都从干净的状态开始
        self.conversation_history.clear()
        logger.info("Cleared conversation history for new analysis session")

        try:
            logger.info("=== Phase 1: Topic Expansion with LLM ===")
            expanded_topic = self._expand_topic_with_llm(topic, description)

            print("expanded_topic:", expanded_topic, "\n")

            logger.info(f"=== Phase 2: Interactive Refinement ({self.max_interaction_rounds} rounds) ===")
            refined_topic = self._interactive_refinement(expanded_topic)

            logger.info("=== Phase 3: Literature Retrieval using LLM Search Host ===")
            literature_results = await self._retrieve_literature(refined_topic, top_n)

            logger.info(f"✅ Analysis completed successfully. Retrieved {len(literature_results)} papers")
            logger.info("📁 Results have been saved by llm_search_server during crawling process")
            return literature_results

        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            raise
    
    def _load_config(self):
        try:
            if self.config_path and os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    "search": {
                        "default_top_n": 20,
                        "default_engine": "google",
                        "similarity_threshold": 80
                    },
                    "interaction": {
                        "timeout_seconds": 0,  # 无超时限制
                        "auto_continue": False
                    }
                }
            logger.info("Configuration loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load config, using defaults: {e}")
            self.config = {}

    def _load_prompt_templates(self):
        self.prompts = {
            "topic_expansion": """
你是一个专业的学术研究分析专家。请对以下研究主题进行深度分析和多角度进行扩写。

原始主题：{topic}
原始主题描述：{description}

请从多个角度进行分析，你将生成一段提示词传递给搜索智能体，他将根据你的提示词生成一些列搜索引擎查询语句，所以请确保你的描述专业且全面。请直接以JSON格式给出你生成的描述。

{{
    "description": "你生成的描述"
}}
""",
            "refinement": """
基于用户反馈，请优化以下研究分析：

当前描述：
{current_analysis}

用户反馈：
{user_feedback}

请根据反馈调整和改进分析内容，保持JSON格式输出。
""",

        }

    def _expand_topic_with_llm(self, topic: str, description: Optional[str] = None) -> Dict[str, Any]:
        logger.info(f"Expanding topic with LLM: '{topic}'")

        prompt = self.prompts["topic_expansion"].format(
            topic=topic,
            description=description or "无额外描述"
        )

        response = self.llm_wrapper.completion(prompt)

        expanded_topic = self._parse_json_response(response)

        # 添加原始主题信息以便后续使用
        if expanded_topic:
            expanded_topic['original_topic'] = topic
            expanded_topic['original_description'] = description
        else:
            # 如果解析失败，创建基本结构
            expanded_topic = {
                "description": description or f"Research on {topic}",
                "original_topic": topic,
                "original_description": description
            }

        logger.info("Topic expansion completed successfully")
        return expanded_topic

    def _parse_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        try:
            if response.strip().startswith('{'):
                return json.loads(response.strip())

            import re
            json_pattern = r'```json\s*(.*?)\s*```'
            match = re.search(json_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group(1).strip())

            brace_pattern = r'\{.*\}'
            match = re.search(brace_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group(0))

            return None

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {e}")
            return None

    def _create_task_directory(self, task: str) -> Path:

        safe_task_name = "".join(c for c in task if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_task_name = safe_task_name.replace(' ', '_')

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        task_dir_name = f"{safe_task_name}_{timestamp}"

        task_dir = self.base_dir / task_dir_name
        task_dir.mkdir(parents=True, exist_ok=True)
        (task_dir / "papers").mkdir(exist_ok=True)
        (task_dir / "analysis").mkdir(exist_ok=True)
        (task_dir / "logs").mkdir(exist_ok=True)

        return task_dir
    
    def _interactive_refinement(self, expanded_topic: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"Starting interactive refinement (max {self.max_interaction_rounds} rounds)")

        current_analysis = expanded_topic.copy()

        for round_num in range(self.max_interaction_rounds):
            logger.info(f"--- Interaction Round {round_num + 1}/{self.max_interaction_rounds} ---")

            self._display_analysis(current_analysis, round_num + 1)

            user_feedback = self._get_user_feedback(round_num + 1)

            if user_feedback.get('satisfied', False):
                logger.info("User satisfied with current analysis, proceeding to literature search")
                break

            if user_feedback.get('feedback_text'):
                logger.info("Refining analysis based on user feedback")
                current_analysis = self._refine_with_feedback(current_analysis, user_feedback)
            else:
                logger.info("No specific feedback provided, keeping current analysis")

        logger.info("Interactive refinement completed")
        return current_analysis

    def _display_analysis(self, analysis: Dict[str, Any], round_num: int):
        """展示当前分析结果给用户"""
        print(f"\n{'='*60}")
        print(f"第 {round_num} 轮分析结果")
        print(f"{'='*60}")
        print(f"\n🎯 我对这个topic的分析结果如下：{analysis.get('description', 'N/A')}")
        

    def _get_user_feedback(self, round_num: int) -> Dict[str, Any]:
        print(f"\n{'='*60}")
        print(f"请提供第 {round_num} 轮反馈")
        print(f"{'='*60}")

        try:
            satisfied_input = input("\n您是否满意当前的分析结果？(y/n/回车继续): ").strip().lower()

            if satisfied_input in ['y', 'yes', '是', 'ok']:
                return {'satisfied': True}

            if satisfied_input in ['', 'continue', '继续']:
                if round_num >= self.max_interaction_rounds:
                    return {'satisfied': True}
                else:
                    return {'satisfied': False}

            # 获取具体反馈
            print("\n请提供具体的改进建议")
            # print("- 需要调整的概念定义")
            # print("- 需要添加或删除的子领域")
            # print("- 需要补充的研究问题")
            # print("- 其他任何改进意见")
            # print("\n输入您的反馈（回车结束）：")

            feedback_text = input().strip()

            return {
                'satisfied': False,
                'feedback_text': feedback_text,
                'round': round_num
            }

        except KeyboardInterrupt:
            logger.info("User interrupted, proceeding with current analysis")
            return {'satisfied': True}
        except Exception as e:
            logger.warning(f"Error getting user feedback: {e}")
            return {'satisfied': True}

    def _refine_with_feedback(self, current_analysis: Dict[str, Any],
                            user_feedback: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("Refining analysis based on user feedback")

        prompt = self.prompts["refinement"].format(
            current_analysis=json.dumps(current_analysis, ensure_ascii=False, indent=2),
            user_feedback=user_feedback.get('feedback_text', '')
        )

        response = self.llm_wrapper.completion(prompt)
        refined_analysis = self._parse_json_response(response)

        if refined_analysis:
            logger.info("Analysis refined successfully")
            return refined_analysis
        else:
            logger.warning("Failed to parse refined analysis, keeping original")
            return current_analysis

    async def _retrieve_literature(self, refined_topic: Dict[str, Any], top_n: int = 20) -> List[Dict[str, Any]]:
        logger.info("Starting literature retrieval using MCP client")

        topic_description = refined_topic.get('description', '')

        # 从描述中提取核心概念
        core_concept = refined_topic.get('core_concept')
        if not core_concept:
            if '：' in topic_description:
                core_concept = topic_description.split('：')[0].strip()
            elif ':' in topic_description:
                core_concept = topic_description.split(':')[0].strip()
            else:
                core_concept = refined_topic.get('original_topic', 'research')

        logger.info(f"Using MCP client for topic: '{core_concept}'")
        logger.info(f"Topic description length: {len(topic_description)} characters")

        # 使用MCP会话执行搜索
        async def search_task(session):
            # 获取可用工具
            tools_response = await session.list_tools()
            available_tools = [tool.model_dump() for tool in tools_response.tools]

            logger.info(f"Available MCP tools: {[tool['name'] for tool in available_tools]}")

            # 执行多轮搜索流程
            return await self._execute_search_workflow(
                session, core_concept, topic_description, top_n, available_tools
            )

        return await self._execute_with_mcp_session(search_task)

    async def _execute_search_workflow(self, session: ClientSession, core_concept: str,
                                       topic_description: str, top_n: int,
                                       available_tools: List[Dict]) -> List[Dict[str, Any]]:
        """执行完整的搜索工作流"""
        try:
            # 步骤1: 生成搜索查询
            logger.info("=== Step 1: Generating search queries ===")
            query_task = f"为主题'{core_concept}'生成搜索查询。描述：{topic_description}"
            query_result = await self._call_mcp_tool_with_llm(session, query_task, available_tools)

            if not query_result or 'result' not in query_result:
                logger.error("Failed to generate search queries")
                return []

            # 步骤2: 执行网络搜索
            logger.info("=== Step 2: Performing web search ===")
            search_task = f"使用生成的查询执行网络搜索，目标获取{top_n}个相关结果"
            search_result = await self._call_mcp_tool_with_llm(session, search_task, available_tools)

            if not search_result or 'result' not in search_result:
                logger.error("Failed to perform web search")
                return []

            # 步骤3: 分析搜索结果并爬取内容
            logger.info("=== Step 3: Analyzing results and crawling content ===")
            crawl_task = f"分析搜索结果的相关性并爬取最相关的{min(top_n, 20)}个URL的内容"
            crawl_result = await self._call_mcp_tool_with_llm(session, crawl_task, available_tools)

            if not crawl_result or 'result' not in crawl_result:
                logger.error("Failed to crawl content")
                return []

            # 处理最终结果
            final_result = crawl_result['result']
            if hasattr(final_result, 'content') and hasattr(final_result.content[0], 'text'):
                # 解析MCP工具返回的结果
                result_text = final_result.content[0].text
                try:
                    literature_data = json.loads(result_text)
                    if isinstance(literature_data, list):
                        logger.info(f"Successfully retrieved {len(literature_data)} literature papers")
                        return literature_data
                    elif isinstance(literature_data, dict) and 'results' in literature_data:
                        results = literature_data['results']
                        logger.info(f"Successfully retrieved {len(results)} literature papers")
                        return results
                except json.JSONDecodeError:
                    logger.warning("Could not parse result as JSON, treating as text")
                    return [{
                        'type': 'text_result',
                        'content': result_text,
                        'topic': core_concept,
                        'description': topic_description
                    }]

            logger.warning("Unexpected result format from MCP tools")
            return []

        except Exception as e:
            logger.error(f"Error in search workflow: {e}")
            return []

async def analyse(task: str, description: Optional[str] = None, top_n: int = 20,
                  max_interaction_rounds: int = 3) -> List[Dict[str, Any]]:
    analyser = AnalyseInterface(max_interaction_rounds=max_interaction_rounds)
    return await analyser.analyse(task, description, top_n)


if __name__ == "__main__":
    import sys

    topic = sys.argv[1]
    description = sys.argv[2] if len(sys.argv) > 2 else None
    top_n = int(sys.argv[3]) if len(sys.argv) > 3 else 20

    print(f"🔍 开始分析主题: {topic}")
    if description:
        print(f"📝 描述: {description}")
    print(f"🎯 目标文献数量: {top_n}")
    print("-" * 50)

    try:
        import asyncio
        literature_results = asyncio.run(analyse(topic, description, top_n))
        print(f"\n✅ 分析完成！检索到 {len(literature_results)} 篇文献")
        print("📁 文献已保存在 llm_search_server 指定的目录中")
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
