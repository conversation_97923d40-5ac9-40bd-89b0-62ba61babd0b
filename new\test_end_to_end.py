#!/usr/bin/env python3
"""
端到端测试：验证完整的analyse工作流
注意：此测试不会执行实际的API调用，只验证工作流结构
"""

import asyncio
import sys
import os
import logging
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from search.analyse import AnalyseInterface

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_llm_tool_selection_simulation():
    """模拟LLM工具选择过程"""
    print("🤖 测试LLM工具选择模拟...")
    
    try:
        analyser = AnalyseInterface()
        
        # 获取可用工具
        async def get_tools_task(session):
            tools_response = await session.list_tools()
            return [tool.model_dump() for tool in tools_response.tools]
        
        available_tools = await analyser._execute_with_mcp_session(get_tools_task)
        
        # 模拟不同的任务描述和工具选择
        test_scenarios = [
            {
                "task": "为主题'机器学习'生成搜索查询",
                "expected_tool": "generate_search_queries",
                "description": "需要生成多个相关的搜索查询"
            },
            {
                "task": "使用查询列表执行网络搜索",
                "expected_tool": "web_search", 
                "description": "需要在网络上搜索相关文献"
            },
            {
                "task": "分析搜索结果的相关性",
                "expected_tool": "analyze_search_results",
                "description": "需要评估搜索结果与主题的相关性"
            },
            {
                "task": "爬取URL列表并提取内容",
                "expected_tool": "crawl_urls",
                "description": "需要获取网页内容并进行处理"
            }
        ]
        
        # 测试工具格式化
        formatted_tools = analyser._format_available_tools(available_tools)
        
        for scenario in test_scenarios:
            print(f"  场景: {scenario['task']}")
            print(f"  期望工具: {scenario['expected_tool']}")
            
            # 验证期望的工具在可用工具中
            tool_names = [tool['name'] for tool in available_tools]
            assert scenario['expected_tool'] in tool_names, f"工具 {scenario['expected_tool']} 不可用"
            
            print(f"  ✅ 工具 {scenario['expected_tool']} 可用")
        
        print("✅ LLM工具选择模拟测试通过")
        return True
        
    except Exception as e:
        print(f"❌ LLM工具选择模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_search_workflow_structure():
    """测试搜索工作流结构"""
    print("\n🔄 测试搜索工作流结构...")
    
    try:
        analyser = AnalyseInterface()
        
        # 准备测试数据
        refined_topic = {
            'original_topic': '人工智能',
            'core_concept': '人工智能',
            'description': '人工智能在医疗诊断中的应用研究',
            'refined': True
        }
        
        print(f"测试主题: {refined_topic['core_concept']}")
        print(f"测试描述: {refined_topic['description']}")
        
        # 验证搜索工作流的结构（不执行实际搜索）
        async def workflow_structure_test(session):
            # 步骤1: 验证工具可用性
            tools_response = await session.list_tools()
            available_tools = [tool.model_dump() for tool in tools_response.tools]
            
            required_workflow_tools = [
                'generate_search_queries',  # 步骤1: 生成查询
                'web_search',              # 步骤2: 网络搜索
                'analyze_search_results',   # 步骤3: 分析结果
                'crawl_urls'               # 步骤4: 爬取内容
            ]
            
            tool_names = [tool['name'] for tool in available_tools]
            
            for required_tool in required_workflow_tools:
                assert required_tool in tool_names, f"工作流缺少必要工具: {required_tool}"
                print(f"  ✅ 工作流工具可用: {required_tool}")
            
            # 验证工具参数结构
            for tool in available_tools:
                assert 'inputSchema' in tool, f"工具 {tool['name']} 缺少输入模式"
                assert 'properties' in tool['inputSchema'], f"工具 {tool['name']} 缺少属性定义"
                print(f"  ✅ 工具参数结构正确: {tool['name']}")
            
            return True
        
        result = await analyser._execute_with_mcp_session(workflow_structure_test)
        print("✅ 搜索工作流结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 搜索工作流结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_memory_management():
    """测试对话记忆管理"""
    print("\n🧠 测试对话记忆管理...")
    
    try:
        analyser = AnalyseInterface()
        
        # 验证记忆初始化
        assert hasattr(analyser, 'search_memory'), "搜索记忆未初始化"
        assert isinstance(analyser.search_memory, list), "搜索记忆不是列表类型"
        assert len(analyser.search_memory) == 0, "搜索记忆初始状态不为空"
        
        print("✅ 搜索记忆初始化正确")
        
        # 模拟记忆添加
        test_memory_entry = {
            "task": "测试任务",
            "tool_used": "generate_search_queries",
            "arguments": {"topic": "测试主题"},
            "reasoning": "测试原因",
            "result": "测试结果"
        }
        
        analyser.search_memory.append(test_memory_entry)
        
        assert len(analyser.search_memory) == 1, "记忆添加失败"
        assert analyser.search_memory[0]['task'] == "测试任务", "记忆内容不正确"
        
        print("✅ 记忆添加功能正确")
        
        # 验证记忆结构
        memory_entry = analyser.search_memory[0]
        required_fields = ['task', 'tool_used', 'arguments', 'reasoning', 'result']
        
        for field in required_fields:
            assert field in memory_entry, f"记忆条目缺少字段: {field}"
        
        print("✅ 记忆结构完整")
        print("✅ 对话记忆管理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 对话记忆管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_error_handling():
    """测试错误处理机制"""
    print("\n⚠️ 测试错误处理机制...")
    
    try:
        analyser = AnalyseInterface()
        
        # 测试无效MCP服务器参数
        print("  测试MCP连接错误处理...")
        
        # 保存原始参数
        original_params = analyser.mcp_server_params
        
        # 设置无效参数
        from mcp.client.stdio import StdioServerParameters
        analyser.mcp_server_params = StdioServerParameters(
            command="invalid_command",
            args=["invalid_args"]
        )
        
        # 测试错误处理
        try:
            async def error_test_task(session):
                return True
            
            await analyser._execute_with_mcp_session(error_test_task)
            print("  ⚠️ 预期的错误未发生")
        except Exception as e:
            print(f"  ✅ 正确捕获MCP连接错误: {type(e).__name__}")
        
        # 恢复原始参数
        analyser.mcp_server_params = original_params
        
        # 验证恢复后正常工作
        async def recovery_test_task(session):
            tools_response = await session.list_tools()
            return len(tools_response.tools)
        
        tool_count = await analyser._execute_with_mcp_session(recovery_test_task)
        assert tool_count == 4, "恢复后工具数量不正确"
        print("  ✅ 错误恢复正常")
        
        print("✅ 错误处理机制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_interface_compatibility():
    """测试接口兼容性"""
    print("\n🔌 测试接口兼容性...")
    
    try:
        # 测试AnalyseInterface接口
        analyser = AnalyseInterface()
        
        # 验证必要方法存在
        required_methods = [
            'analyse',
            '_retrieve_literature', 
            '_execute_with_mcp_session',
            '_execute_search_workflow',
            '_call_mcp_tool_with_llm',
            '_format_available_tools'
        ]
        
        for method_name in required_methods:
            assert hasattr(analyser, method_name), f"缺少方法: {method_name}"
            method = getattr(analyser, method_name)
            assert callable(method), f"方法不可调用: {method_name}"
            print(f"  ✅ 方法存在且可调用: {method_name}")
        
        # 测试顶层函数接口
        from search.analyse import analyse
        assert callable(analyse), "顶层analyse函数不可调用"
        print("  ✅ 顶层analyse函数可调用")
        
        # 验证参数兼容性
        import inspect
        sig = inspect.signature(analyser.analyse)
        params = list(sig.parameters.keys())
        
        # 验证关键参数存在
        assert 'topic' in params, "缺少topic参数"
        assert 'top_n' in params, "缺少top_n参数"
        print("  ✅ 参数兼容性正确")
        
        print("✅ 接口兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 接口兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_end_to_end_test():
    """运行端到端测试"""
    print("🎯 开始端到端测试...")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("LLM工具选择模拟", test_llm_tool_selection_simulation),
        ("搜索工作流结构", test_search_workflow_structure),
        ("对话记忆管理", test_memory_management),
        ("错误处理机制", test_error_handling),
        ("接口兼容性", test_interface_compatibility),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("🏁 端到端测试结果汇总:")
    print("="*60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:.<25} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed + failed} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    print(f"成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 端到端测试全部通过！")
        print("🚀 analyse框架已准备就绪，可以进行实际使用！")
        return 0
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(run_end_to_end_test())
    sys.exit(exit_code)
