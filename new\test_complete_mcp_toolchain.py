#!/usr/bin/env python3
"""
测试完整的MCP工具链
"""

import asyncio
import json
import logging
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        env_config = json.load(f)
    
    # 设置API密钥
    search_engines = env_config.get("api_keys", {}).get("search_engines", {})
    if search_engines.get("serpapi_key"):
        os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
        print(f"  ✅ SERPAPI_KEY 已设置")
    
    # 设置OpenAI配置
    openai_config = env_config.get("api_keys", {}).get("openai", {})
    if openai_config.get("api_key"):
        os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
        print(f"  ✅ OPENAI_API_KEY 已设置")
    if openai_config.get("base_url"):
        os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
        print(f"  ✅ OPENAI_BASE_URL 已设置")

async def test_complete_toolchain():
    """测试完整的MCP工具链"""
    print("\n🔌 启动MCP客户端...")
    
    try:
        from mcp import ClientSession, StdioServerParameters
        from mcp.client.stdio import stdio_client
        
        # 启动MCP客户端
        server_params = StdioServerParameters(
            command="python",
            args=[os.path.join(os.path.dirname(__file__), "src", "search", "llm_search_mcp_server.py")],
            env=dict(os.environ)
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("  ✅ MCP客户端连接成功")
                
                # 初始化
                await session.initialize()
                print("  ✅ MCP会话初始化成功")
                
                # 列出可用工具
                tools = await session.list_tools()
                available_tools = [tool.name for tool in tools.tools]
                print(f"  📋 可用工具: {available_tools}")
                
                # 测试1: generate_search_queries
                print(f"\n🔍 测试1: generate_search_queries...")
                try:
                    result = await session.call_tool(
                        "generate_search_queries",
                        arguments={
                            "topic": "深度学习在计算机视觉中的应用",
                            "num_queries": 3
                        }
                    )
                    
                    if result.content and len(result.content) > 0:
                        content = json.loads(result.content[0].text)
                        queries = content.get("queries", [])
                        print(f"  ✅ 生成了 {len(queries)} 个查询:")
                        for i, query in enumerate(queries[:3]):
                            print(f"     {i+1}. {query}")
                    else:
                        print(f"  ❌ 未生成查询")
                        return False
                        
                except Exception as e:
                    print(f"  ❌ generate_search_queries失败: {e}")
                    return False
                
                # 测试2: web_search
                print(f"\n🌐 测试2: web_search...")
                try:
                    result = await session.call_tool(
                        "web_search",
                        arguments={
                            "queries": ["深度学习", "计算机视觉"],
                            "topic": "人工智能",
                            "top_n": 3
                        }
                    )
                    
                    if result.content and len(result.content) > 0:
                        content = json.loads(result.content[0].text)
                        urls = content.get("urls", [])
                        print(f"  ✅ 获取了 {len(urls)} 个URL:")
                        for i, url in enumerate(urls[:3]):
                            print(f"     {i+1}. {url}")
                        
                        # 保存URL用于后续测试
                        test_urls = urls[:2]  # 取前2个URL用于测试
                    else:
                        print(f"  ❌ 未获取到URL")
                        return False
                        
                except Exception as e:
                    print(f"  ❌ web_search失败: {e}")
                    return False
                
                # 测试3: analyze_search_results
                print(f"\n📊 测试3: analyze_search_results...")
                try:
                    result = await session.call_tool(
                        "analyze_search_results",
                        arguments={
                            "urls": test_urls,
                            "topic": "深度学习在计算机视觉中的应用",
                            "max_results": 2
                        }
                    )
                    
                    if result.content and len(result.content) > 0:
                        content = json.loads(result.content[0].text)
                        analyzed_urls = content.get("analyzed_urls", [])
                        print(f"  ✅ 分析了 {len(analyzed_urls)} 个URL")
                        print(f"  📈 平均相关性评分: {content.get('average_score', 0):.2f}")
                    else:
                        print(f"  ❌ 分析失败")
                        return False
                        
                except Exception as e:
                    print(f"  ❌ analyze_search_results失败: {e}")
                    return False
                
                # 测试4: crawl_urls (简化测试，只爬取1个URL)
                print(f"\n🕷️ 测试4: crawl_urls...")
                try:
                    result = await session.call_tool(
                        "crawl_urls",
                        arguments={
                            "url_list": [test_urls[0]],  # 只测试第一个URL
                            "topic": "深度学习",
                            "max_length": 1000
                        }
                    )
                    
                    if result.content and len(result.content) > 0:
                        content = json.loads(result.content[0].text)
                        crawled_results = content.get("results", [])
                        print(f"  ✅ 爬取了 {len(crawled_results)} 个页面")
                        if crawled_results:
                            first_result = crawled_results[0]
                            print(f"  📄 第一个页面标题: {first_result.get('title', 'N/A')}")
                            content_length = len(first_result.get('content', ''))
                            print(f"  📝 内容长度: {content_length} 字符")
                    else:
                        print(f"  ❌ 爬取失败")
                        return False
                        
                except Exception as e:
                    print(f"  ❌ crawl_urls失败: {e}")
                    return False
                
                print(f"\n✅ 所有工具测试完成!")
                return True
                
    except Exception as e:
        print(f"❌ MCP工具链测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始完整MCP工具链测试")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    # 测试工具链
    success = asyncio.run(test_complete_toolchain())
    
    if success:
        print("\n🎉 完整工具链测试成功!")
        return 0
    else:
        print("\n💥 工具链测试失败!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
