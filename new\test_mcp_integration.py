#!/usr/bin/env python3
"""
测试MCP客户端集成的简单脚本
"""

import asyncio
import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from search.analyse import AnalyseInterface

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_mcp_integration():
    """测试MCP客户端集成"""
    print("🔧 开始测试MCP客户端集成...")
    
    try:
        # 创建分析接口实例
        analyser = AnalyseInterface(max_interaction_rounds=1)
        print("✅ AnalyseInterface 创建成功")
        
        # 测试MCP会话创建和工具获取
        print("🔗 测试MCP会话创建和工具获取...")

        async def test_session_task(session):
            # 测试工具列表获取
            print("🛠️ 获取可用工具...")
            tools_response = await session.list_tools()
            available_tools = [tool.model_dump() for tool in tools_response.tools]

            print(f"✅ 获取到 {len(available_tools)} 个可用工具:")
            for tool in available_tools:
                print(f"  - {tool['name']}: {tool['description']}")

            return available_tools

        available_tools = await analyser._execute_with_mcp_session(test_session_task)
        print("✅ MCP会话测试成功")
        
        print("\n🎉 MCP客户端集成测试完成！所有功能正常工作。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_search():
    """测试简单搜索功能"""
    print("\n🔍 开始测试简单搜索功能...")
    
    try:
        # 创建分析接口实例
        analyser = AnalyseInterface(max_interaction_rounds=1)
        
        # 执行简单的文献检索测试
        test_topic = "machine learning"
        test_description = "A brief overview of machine learning algorithms and applications"
        
        print(f"📝 测试主题: {test_topic}")
        print(f"📄 测试描述: {test_description}")
        
        # 注意：这里只测试到MCP会话创建，不执行完整搜索以避免API调用
        async def search_prep_task(session):
            tools_response = await session.list_tools()
            return len(tools_response.tools)

        tool_count = await analyser._execute_with_mcp_session(search_prep_task)
        print(f"✅ 搜索准备就绪，可用工具: {tool_count} 个")
        print("✅ 简单搜索测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 启动MCP集成测试...")
    
    async def main():
        # 测试MCP集成
        integration_ok = await test_mcp_integration()
        
        if integration_ok:
            # 测试搜索功能
            search_ok = await test_simple_search()
            
            if search_ok:
                print("\n🎊 所有测试通过！MCP客户端集成成功。")
                return 0
            else:
                print("\n⚠️ 搜索测试失败")
                return 1
        else:
            print("\n❌ MCP集成测试失败")
            return 1
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
