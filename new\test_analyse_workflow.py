#!/usr/bin/env python3
"""
测试analyse.py的完整工作流程
"""

import asyncio
import json
import logging
import os
import sys
from unittest.mock import patch
from io import StringIO

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        env_config = json.load(f)
    
    # 设置API密钥
    search_engines = env_config.get("api_keys", {}).get("search_engines", {})
    if search_engines.get("serpapi_key"):
        os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
        print(f"  ✅ SERPAPI_KEY 已设置")
    
    # 设置OpenAI配置
    openai_config = env_config.get("api_keys", {}).get("openai", {})
    if openai_config.get("api_key"):
        os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
        print(f"  ✅ OPENAI_API_KEY 已设置")
    if openai_config.get("base_url"):
        os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
        print(f"  ✅ OPENAI_BASE_URL 已设置")

def mock_user_input():
    """模拟用户输入，避免交互式输入"""
    # 模拟用户在第一轮就满意分析结果
    return "y"

async def test_analyse_basic_workflow():
    """测试analyse的基本工作流程"""
    print("\n🔍 测试analyse基本工作流程...")
    
    try:
        from search.analyse import AnalyseInterface
        
        # 创建analyse实例
        analyser = AnalyseInterface(
            base_dir="new/test_analyse",
            max_interaction_rounds=1,  # 减少交互轮数用于测试
            llm_model="gemini-2.0-flash-thinking-exp-01-21",
            llm_infer_type="OpenAI"
        )
        
        print("  ✅ AnalyseInterface 初始化成功")
        
        # 模拟用户输入，避免交互式输入
        with patch('builtins.input', side_effect=['y']):
            # 测试analyse方法
            results = await analyser.analyse(
                topic="深度学习在计算机视觉中的应用",
                description="研究深度学习技术在图像识别、目标检测等计算机视觉任务中的应用",
                top_n=5  # 减少数量用于测试
            )
            
            print(f"  ✅ analyse方法执行成功")
            print(f"  📊 返回结果数量: {len(results)}")
            
            if results:
                print(f"  📄 第一个结果示例:")
                first_result = results[0]
                for key, value in first_result.items():
                    if isinstance(value, str) and len(value) > 100:
                        print(f"    {key}: {value[:100]}...")
                    else:
                        print(f"    {key}: {value}")
            
            return True
            
    except Exception as e:
        print(f"  ❌ analyse工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_analyse_mcp_integration():
    """测试analyse与MCP的集成"""
    print("\n🔌 测试analyse与MCP集成...")
    
    try:
        from search.analyse import AnalyseInterface
        
        # 创建analyse实例
        analyser = AnalyseInterface(
            base_dir="new/test_mcp_integration",
            max_interaction_rounds=1,
            llm_model="gemini-2.0-flash-thinking-exp-01-21"
        )
        
        # 测试MCP客户端配置
        print("  🔧 检查MCP客户端配置...")
        assert hasattr(analyser, 'mcp_server_params'), "MCP服务器参数未配置"
        assert analyser.mcp_server_params.command == "python", "MCP命令配置错误"
        print("  ✅ MCP客户端配置正确")
        
        # 测试环境变量传递
        print("  🌍 检查环境变量传递...")
        env_vars = analyser.mcp_server_params.env
        assert "SERPAPI_KEY" in env_vars, "SERPAPI_KEY未传递"
        assert "OPENAI_API_KEY" in env_vars, "OPENAI_API_KEY未传递"
        print("  ✅ 环境变量传递正确")
        
        # 测试MCP会话执行
        print("  🔄 测试MCP会话执行...")
        
        async def test_session_task(session):
            # 测试会话初始化
            await session.initialize()
            
            # 测试工具列表
            tools = await session.list_tools()
            tool_names = [tool.name for tool in tools.tools]
            
            expected_tools = ['generate_search_queries', 'web_search', 'analyze_search_results', 'crawl_urls']
            for tool in expected_tools:
                assert tool in tool_names, f"工具 {tool} 未找到"
            
            print(f"    📋 可用工具: {tool_names}")
            return True
        
        result = await analyser._execute_with_mcp_session(test_session_task)
        assert result is True, "MCP会话测试失败"
        
        print("  ✅ MCP集成测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ MCP集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_analyse_multi_round():
    """测试analyse的多轮交互"""
    print("\n🔄 测试analyse多轮交互...")
    
    try:
        from search.analyse import AnalyseInterface
        
        # 创建analyse实例，设置多轮交互
        analyser = AnalyseInterface(
            base_dir="new/test_multi_round",
            max_interaction_rounds=2,  # 设置2轮交互
            llm_model="gemini-2.0-flash-thinking-exp-01-21"
        )
        
        # 模拟多轮用户输入：第一轮提供反馈，第二轮满意
        mock_inputs = [
            "n",  # 第一轮不满意
            "请更加关注深度学习在医疗图像分析中的应用",  # 第一轮反馈
            "y"   # 第二轮满意
        ]
        
        with patch('builtins.input', side_effect=mock_inputs):
            # 测试多轮交互
            results = await analyser.analyse(
                topic="深度学习在医疗领域的应用",
                description="研究深度学习在医疗诊断和治疗中的应用",
                top_n=3
            )
            
            print(f"  ✅ 多轮交互测试成功")
            print(f"  📊 最终结果数量: {len(results)}")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 多轮交互测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始analyse工作流程测试")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    # 运行测试
    tests = [
        ("MCP集成测试", test_analyse_mcp_integration),
        ("基本工作流程测试", test_analyse_basic_workflow),
        ("多轮交互测试", test_analyse_multi_round),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = asyncio.run(test_func())
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("📊 测试结果汇总")
    print(f"{'='*60}")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！analyse工作流程正常")
        return 0
    else:
        print("💥 部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
