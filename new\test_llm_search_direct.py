#!/usr/bin/env python3
"""
直接测试LLM_search的get_queries方法，绕过MCP
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

def test_llm_search_direct():
    """直接测试LLM_search的get_queries方法"""
    print("🧪 直接测试LLM_search.get_queries方法")
    print("="*50)
    
    try:
        # 导入LLM_search
        from src.search.LLM_search import LLM_search
        
        print("📋 创建LLM_search实例...")
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI"
        )
        print("✅ LLM_search实例创建成功")
        
        # 测试get_queries方法
        print("🔧 测试get_queries方法...")
        topic = "machine learning basics"
        description = "搜索机器学习基础知识"
        
        print(f"主题: {topic}")
        print(f"描述: {description}")
        print("⏳ 调用get_queries...")
        
        queries = llm_search.get_queries(topic=topic, description=description)
        
        print("✅ get_queries调用成功!")
        print(f"📊 生成的查询数量: {len(queries)}")
        print("📊 查询列表:")
        for i, query in enumerate(queries, 1):
            print(f"  {i}. {query}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 LLM_search直接测试")
    print("目标：验证LLM_search.get_queries方法是否正常工作")
    print()
    
    success = test_llm_search_direct()
    
    if success:
        print("\n🎉 LLM_search直接测试通过!")
        print("✅ get_queries方法工作正常")
        print("💡 问题可能在MCP Server的异步处理")
    else:
        print("\n❌ LLM_search直接测试失败!")
        print("❌ get_queries方法本身有问题")

if __name__ == "__main__":
    main()
