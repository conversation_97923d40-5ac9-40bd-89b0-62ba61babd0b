#!/usr/bin/env python3
"""
测试单独的web_search工具调用
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_web_search_tool():
    """测试web_search工具"""
    print("🔧 开始web_search工具测试")
    print("=" * 60)
    
    try:
        # MCP服务器参数
        mcp_server_params = StdioServerParameters(
            command="python",
            args=["-m", "src.search.llm_search_mcp_server"],
            env={
                "PYTHONPATH": ".",
                "OPENAI_API_KEY": "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146",
                "OPENAI_BASE_URL": "https://api.shubiaobiao.cn/v1"
            }
        )
        
        print("🔗 开始MCP会话...")
        
        async with stdio_client(mcp_server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ MCP会话初始化成功")
                
                # 测试简单的web_search调用
                tool_name = "web_search"
                arguments = {
                    "queries": ["机器学习"],  # 只用一个简单查询
                    "topic": "机器学习",
                    "top_n": 5,  # 减少结果数量
                    "engine": "google"
                }
                
                print(f"🛠️ 测试工具调用: {tool_name}")
                print(f"📝 参数: {arguments}")
                
                try:
                    # 设置较短的超时时间
                    result = await asyncio.wait_for(
                        session.call_tool(tool_name, arguments),
                        timeout=15.0  # 15秒超时
                    )
                    print("✅ 工具调用成功!")
                    print(f"📊 结果类型: {type(result)}")
                    
                    if hasattr(result, 'content'):
                        print(f"📄 内容长度: {len(result.content)}")
                        if result.content and hasattr(result.content[0], 'text'):
                            text_content = result.content[0].text
                            print(f"📝 文本内容预览: {text_content[:300]}...")
                    
                    return True
                    
                except asyncio.TimeoutError:
                    print("❌ 工具调用超时（15秒）")
                    return False
                except Exception as e:
                    print(f"❌ 工具调用失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print(f"🚀 开始web_search工具测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        success = await test_web_search_tool()
        
        if success:
            print("\n✅ web_search工具测试通过!")
            return 0
        else:
            print("\n❌ web_search工具测试失败!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
