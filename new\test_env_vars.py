#!/usr/bin/env python3
"""
测试环境变量传递
"""

import os
import sys
import json

def test_env_vars():
    """测试环境变量"""
    print("🔧 环境变量测试")
    print("=" * 50)
    
    # 检查当前进程的环境变量
    print("📋 当前进程环境变量:")
    env_vars_to_check = [
        "SERPAPI_KEY",
        "SERP_API_KEY", 
        "BING_SEARCH_V7_SUBSCRIPTION_KEY",
        "OPENAI_API_KEY",
        "OPENAI_BASE_URL"
    ]
    
    for var in env_vars_to_check:
        value = os.getenv(var)
        if value:
            print(f"  ✅ {var}: {value[:10]}...")
        else:
            print(f"  ❌ {var}: 未设置")
    
    # 从配置文件读取
    print(f"\n📄 配置文件内容:")
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_keys = config.get("api_keys", {})
        search_engines = api_keys.get("search_engines", {})
        
        print(f"  SERPAPI_KEY in config: {search_engines.get('serpapi_key', 'NOT_FOUND')[:10]}...")
        print(f"  BING_KEY in config: {search_engines.get('bing_subscription_key', 'NOT_FOUND')}")
        
    except Exception as e:
        print(f"  ❌ 读取配置文件失败: {e}")
    
    # 测试LLM_search类的导入和初始化
    print(f"\n🔍 测试LLM_search类:")
    
    try:
        # 添加项目路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from search.LLM_search import LLM_search
        print(f"  ✅ LLM_search类导入成功")
        
        # 尝试创建实例
        try:
            llm_search = LLM_search(
                model="gemini-2.0-flash-thinking-exp-01-21",
                infer_type="OpenAI",
                engine="google",
                each_query_result=10
            )
            print(f"  ✅ LLM_search实例创建成功")
            print(f"  🔑 实例中的API密钥:")
            print(f"     serpapi_key: {'已设置' if llm_search.serpapi_key else '未设置'}")
            print(f"     bing_key: {'已设置' if llm_search.bing_subscription_key else '未设置'}")
            
        except Exception as e:
            print(f"  ❌ LLM_search实例创建失败: {e}")
            
    except Exception as e:
        print(f"  ❌ LLM_search类导入失败: {e}")

if __name__ == "__main__":
    test_env_vars()
