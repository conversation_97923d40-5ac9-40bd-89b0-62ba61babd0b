{"servers": {"llm_search_server": {"command": "python", "args": ["-m", "src.search.llm_search_mcp_server"], "env": {"PYTHONPATH": ".", "OPENAI_API_KEY": "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146", "OPENAI_API_BASE": "https://api.shubiaobiao.cn/v1", "OPENAI_BASE_URL": "https://api.shubiaobiao.cn/v1", "GOOGLE_API_KEY": "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146", "SERP_API_KEY": "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1", "SERPAPI_KEY": "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1", "BING_SEARCH_V7_SUBSCRIPTION_KEY": ""}}}, "mcpServers": {"llm_search_mcp": {"command": "python", "args": ["-m", "src.search.llm_search_mcp_server"], "env": {"PYTHONPATH": ".", "OPENAI_API_KEY": "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146", "OPENAI_API_BASE": "https://api.shubiaobiao.cn/v1", "OPENAI_BASE_URL": "https://api.shubiaobiao.cn/v1", "GOOGLE_API_KEY": "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146", "SERP_API_KEY": "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1", "SERPAPI_KEY": "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1", "BING_SEARCH_V7_SUBSCRIPTION_KEY": ""}}}, "tools": [{"name": "generate_search_queries", "description": "基于LLM生成优化的搜索查询", "inputSchema": {"type": "object", "properties": {"topic": {"type": "string", "description": "搜索主题"}, "description": {"type": "string", "description": "主题描述"}, "model": {"type": "string", "description": "使用的LLM模型", "default": "gemini-2.0-flash-thinking-exp-01-21"}}, "required": ["topic"]}}, {"name": "web_search", "description": "使用提供的查询执行网络搜索", "inputSchema": {"type": "object", "properties": {"queries": {"type": "array", "items": {"type": "string"}, "description": "搜索查询列表"}, "topic": {"type": "string", "description": "主要主题"}, "top_n": {"type": "integer", "description": "返回的最相关URL数量", "default": 20}, "engine": {"type": "string", "description": "搜索引擎", "enum": ["google", "bing", "baidu"], "default": "google"}}, "required": ["queries", "topic"]}}, {"name": "analyze_search_results", "description": "分析和过滤搜索结果的相关性", "inputSchema": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}, "description": "要分析的URL列表"}, "topic": {"type": "string", "description": "主要主题"}, "max_results": {"type": "integer", "description": "返回的最大结果数", "default": 10}}, "required": ["urls", "topic"]}}, {"name": "crawl_urls", "description": "异步爬取URL列表并处理内容", "inputSchema": {"type": "object", "properties": {"topic": {"type": "string", "description": "主题，用于内容过滤"}, "url_list": {"type": "array", "items": {"type": "string"}, "description": "要爬取的URL列表"}, "top_n": {"type": "integer", "description": "返回的最相关内容数量", "default": 80}, "model": {"type": "string", "description": "使用的LLM模型", "default": "gemini-2.0-flash-thinking-exp-01-21"}, "similarity_threshold": {"type": "number", "description": "相似度阈值", "default": 80}, "min_length": {"type": "integer", "description": "最小内容长度", "default": 350}, "max_length": {"type": "integer", "description": "最大内容长度", "default": 20000}}, "required": ["topic", "url_list"]}}], "server_config": {"default_model": "gemini-2.0-flash-thinking-exp-01-21", "default_engine": "google", "default_each_query_result": 10, "default_top_n": 20, "default_similarity_threshold": 80, "default_min_length": 350, "default_max_length": 20000}}