#!/usr/bin/env python3
"""
测试API配置修复
验证OpenAI API调用是否正常工作
"""

import os
import sys
import json

# 添加项目根目录到路径
project_root = os.path.join(os.path.dirname(__file__))
sys.path.insert(0, project_root)

def load_and_set_environment():
    """加载环境配置并设置环境变量"""
    config_path = os.path.join(project_root, "config", "environment_config.json")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 设置OpenAI配置
        openai_config = config.get("api_keys", {}).get("openai", {})
        if openai_config.get("api_key"):
            os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
            print(f"✅ OPENAI_API_KEY 已设置: {openai_config['api_key'][:10]}...")
        
        if openai_config.get("base_url"):
            os.environ["OPENAI_API_BASE"] = openai_config["base_url"]
            os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]  # 设置两个变量以确保兼容性
            print(f"✅ OPENAI_BASE_URL 已设置: {openai_config['base_url']}")
        
        # 获取模型配置
        models_config = config.get("models", {})
        default_model = models_config.get("default_model", "gpt-3.5-turbo")
        print(f"📝 使用模型: {default_model}")
        
        return default_model
        
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return "gpt-3.5-turbo"

def test_openai_request():
    """测试OpenAI请求"""
    print("\n🧪 测试OpenAI API请求...")
    
    try:
        from request.wrapper import RequestWrapper
        
        # 获取模型名称
        model = load_and_set_environment()
        
        # 创建请求包装器
        request_wrapper = RequestWrapper(
            model=model,
            infer_type="OpenAI"
        )
        
        # 测试简单请求
        test_message = "请用一句话回答：什么是人工智能？"
        print(f"📤 发送测试消息: {test_message}")
        
        response = request_wrapper.completion(test_message)
        print(f"📥 收到响应: {response[:100]}...")
        
        print("✅ OpenAI API测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 API配置修复测试")
    print("=" * 60)
    
    success = test_openai_request()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 API配置修复成功! 可以继续测试analyse模块")
    else:
        print("💥 API配置仍有问题，需要进一步调试")
    print("=" * 60)

if __name__ == "__main__":
    main()
