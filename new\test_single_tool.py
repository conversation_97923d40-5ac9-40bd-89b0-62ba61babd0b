#!/usr/bin/env python3
"""
测试单个工具调用，验证MCP连接和工具执行
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.search.llm_search_mcp_client import create_mcp_client_from_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_single_tool_call():
    """测试单个工具调用"""
    print("🧪 测试单个工具调用")
    print("="*40)
    
    try:
        # 创建MCP客户端
        print("📋 创建MCP客户端...")
        client = await create_mcp_client_from_config()
        print("✅ MCP客户端创建成功")
        
        # 测试generate_search_queries工具
        print("🔧 测试generate_search_queries工具...")
        tool_args = {
            "topic": "machine learning basics",
            "description": "搜索机器学习基础知识",
            "model": "default"
        }
        
        print(f"工具参数: {tool_args}")
        
        # 执行工具调用
        print("⏳ 执行工具调用...")
        result = await client.call_tool("generate_search_queries", tool_args)
        print("✅ 工具调用成功!")
        
        print(f"📊 结果类型: {type(result)}")
        if isinstance(result, dict):
            print(f"📊 结果键: {list(result.keys())}")
            if "queries" in result:
                print(f"📊 生成的查询数量: {len(result['queries'])}")
                print(f"📊 查询示例: {result['queries'][:3]}")
        
        # 断开连接
        await client.disconnect()
        print("✅ 客户端断开连接")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_parameter_filling():
    """测试参数填充逻辑"""
    print("\n🧪 测试参数填充逻辑")
    print("="*40)
    
    try:
        from src.search.llm_search_host_enhanced import ParameterFiller
        
        filler = ParameterFiller()
        
        # 模拟提取的数据
        extracted_data = {
            "search_queries": ["machine learning", "ML algorithms", "neural networks"],
            "urls": ["http://example1.com", "http://example2.com"],
            "crawled_content": [],
            "analysis_results": []
        }
        
        # 测试web_search参数填充
        print("📋 测试web_search参数填充...")
        web_args = {"topic": "machine learning"}
        filled_web_args = filler.fill_params("web_search", web_args, extracted_data)
        print(f"原始参数: {web_args}")
        print(f"填充后参数: {filled_web_args}")
        print(f"✅ queries自动填充: {'queries' in filled_web_args}")
        
        # 测试crawl_urls参数填充
        print("\n📋 测试crawl_urls参数填充...")
        crawl_args = {}
        filled_crawl_args = filler.fill_params("crawl_urls", crawl_args, extracted_data)
        print(f"原始参数: {crawl_args}")
        print(f"填充后参数: {filled_crawl_args}")
        print(f"✅ url_list自动填充: {'url_list' in filled_crawl_args}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数填充测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 单个工具调用测试")
    print("目标：验证MCP连接和工具执行是否正常")
    print()
    
    # 测试单个工具调用
    success1 = await test_single_tool_call()
    
    # 测试参数填充
    success2 = await test_parameter_filling()
    
    if success1 and success2:
        print("\n🎉 所有测试通过!")
        print("✅ MCP连接正常")
        print("✅ 工具调用正常")
        print("✅ 参数填充正常")
    else:
        print("\n❌ 部分测试失败!")
        if not success1:
            print("❌ MCP连接或工具调用有问题")
        if not success2:
            print("❌ 参数填充有问题")

if __name__ == "__main__":
    asyncio.run(main())
