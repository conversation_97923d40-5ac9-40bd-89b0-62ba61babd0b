#!/usr/bin/env python3
"""
测试修复后的analyse工作流程 - 验证工具间数据传递
"""

import asyncio
import json
import logging
import os
import sys
from unittest.mock import patch

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        env_config = json.load(f)
    
    # 设置API密钥
    search_engines = env_config.get("api_keys", {}).get("search_engines", {})
    if search_engines.get("serpapi_key"):
        os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
        print(f"  ✅ SERPAPI_KEY 已设置")
    
    # 设置OpenAI配置
    openai_config = env_config.get("api_keys", {}).get("openai", {})
    if openai_config.get("api_key"):
        os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
        print(f"  ✅ OPENAI_API_KEY 已设置")
    if openai_config.get("base_url"):
        os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
        print(f"  ✅ OPENAI_BASE_URL 已设置")

async def test_fixed_workflow():
    """测试修复后的工作流程"""
    print("\n🔧 测试修复后的analyse工作流程...")
    
    try:
        from search.analyse import AnalyseInterface
        
        # 创建analyse实例
        analyser = AnalyseInterface(
            base_dir="new/test_fixed_workflow",
            max_interaction_rounds=1,  # 减少交互轮数用于测试
            llm_model="gemini-2.0-flash-thinking-exp-01-21",
            llm_infer_type="OpenAI"
        )
        
        print("  ✅ AnalyseInterface 初始化成功")
        
        # 模拟用户输入，避免交互式输入
        with patch('builtins.input', side_effect=['y']):
            # 测试一个简单的主题，期望能获得实际结果
            results = await analyser.analyse(
                topic="深度学习图像分类",
                description="研究深度学习在图像分类任务中的应用和最新进展",
                top_n=3  # 减少数量用于测试
            )
            
            print(f"  ✅ analyse方法执行成功")
            print(f"  📊 返回结果数量: {len(results)}")
            
            if results:
                print(f"  🎉 成功获取到文献结果！")
                print(f"  📄 第一个结果示例:")
                first_result = results[0]
                for key, value in first_result.items():
                    if isinstance(value, str) and len(value) > 100:
                        print(f"    {key}: {value[:100]}...")
                    else:
                        print(f"    {key}: {value}")
                
                # 检查搜索记忆是否正确记录了工具调用历史
                print(f"\n  🧠 搜索记忆历史:")
                for i, memory in enumerate(analyser.search_memory, 1):
                    print(f"    步骤{i}: {memory['tool_used']} - {memory['task'][:50]}...")
                
                return True
            else:
                print(f"  ⚠️ 虽然执行成功，但仍然没有获取到文献结果")
                print(f"  🧠 搜索记忆历史:")
                for i, memory in enumerate(analyser.search_memory, 1):
                    print(f"    步骤{i}: {memory['tool_used']} - {memory['task'][:50]}...")
                    print(f"      参数: {memory['arguments']}")
                return False
            
    except Exception as e:
        print(f"  ❌ 修复后工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tool_data_passing():
    """专门测试工具间数据传递"""
    print("\n🔗 测试工具间数据传递...")
    
    try:
        from search.analyse import AnalyseInterface
        
        # 创建analyse实例
        analyser = AnalyseInterface(
            base_dir="new/test_data_passing",
            max_interaction_rounds=1,
            llm_model="gemini-2.0-flash-thinking-exp-01-21"
        )
        
        # 手动执行MCP会话来观察数据传递
        async def test_session_task(session):
            await session.initialize()
            
            # 获取可用工具
            tools_response = await session.list_tools()
            available_tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                }
                for tool in tools_response.tools
            ]
            
            print(f"    📋 可用工具: {[tool['name'] for tool in available_tools]}")
            
            # 测试完整的搜索工作流
            results = await analyser._execute_search_workflow(
                session=session,
                core_concept="深度学习图像分类",
                topic_description="研究深度学习在图像分类任务中的应用",
                top_n=2,
                available_tools=available_tools
            )
            
            print(f"    📊 工作流程返回结果数量: {len(results)}")
            
            # 检查搜索记忆
            print(f"    🧠 工具调用历史:")
            for i, memory in enumerate(analyser.search_memory, 1):
                print(f"      步骤{i}: {memory['tool_used']}")
                print(f"        任务: {memory['task'][:60]}...")
                print(f"        参数: {memory['arguments']}")
                print(f"        推理: {memory['reasoning'][:60]}...")
            
            return len(results) > 0
        
        result = await analyser._execute_with_mcp_session(test_session_task)
        
        if result:
            print("  ✅ 工具间数据传递测试成功")
        else:
            print("  ⚠️ 工具间数据传递测试完成，但未获得结果")
        
        return result
        
    except Exception as e:
        print(f"  ❌ 工具间数据传递测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始修复后的analyse工作流程测试")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    # 运行测试
    tests = [
        ("工具间数据传递测试", test_tool_data_passing),
        ("修复后完整工作流程测试", test_fixed_workflow),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = asyncio.run(test_func())
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("📊 修复测试结果汇总")
    print(f"{'='*60}")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 测试通过")
    
    if success_count == len(results):
        print("🎉 修复成功！analyse工作流程现在能正确传递数据并获取文献结果")
        return 0
    else:
        print("💥 修复仍有问题，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
