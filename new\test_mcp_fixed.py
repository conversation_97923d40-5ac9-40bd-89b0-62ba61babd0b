#!/usr/bin/env python3
"""
测试修复后的MCP服务器web_search功能
"""

import asyncio
import json
import logging
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_mcp_web_search():
    """测试MCP web_search工具"""
    print("🚀 开始测试修复后的MCP web_search功能")
    print("=" * 60)
    
    try:
        from mcp import ClientSession, StdioServerParameters
        from mcp.client.stdio import stdio_client
        
        # 设置环境变量
        print("🔧 设置环境变量...")
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'environment_config.json')
        
        with open(config_path, 'r', encoding='utf-8') as f:
            env_config = json.load(f)
        
        # 设置API密钥
        search_engines = env_config.get("api_keys", {}).get("search_engines", {})
        if search_engines.get("serpapi_key"):
            os.environ["SERPAPI_KEY"] = search_engines["serpapi_key"]
            print(f"  ✅ SERPAPI_KEY 已设置: {search_engines['serpapi_key'][:10]}...")
        
        # 设置OpenAI配置
        openai_config = env_config.get("api_keys", {}).get("openai", {})
        if openai_config.get("api_key"):
            os.environ["OPENAI_API_KEY"] = openai_config["api_key"]
            print(f"  ✅ OPENAI_API_KEY 已设置")
        if openai_config.get("base_url"):
            os.environ["OPENAI_BASE_URL"] = openai_config["base_url"]
            print(f"  ✅ OPENAI_BASE_URL 已设置")
        
        # 启动MCP客户端
        print("\n🔌 启动MCP客户端...")
        server_params = StdioServerParameters(
            command="python",
            args=[os.path.join(os.path.dirname(__file__), "src", "search", "llm_search_mcp_server.py")],
            env=dict(os.environ)  # 传递当前环境变量
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("  ✅ MCP客户端连接成功")
                
                # 初始化
                await session.initialize()
                print("  ✅ MCP会话初始化成功")
                
                # 列出可用工具
                tools = await session.list_tools()
                print(f"  📋 可用工具: {[tool.name for tool in tools.tools]}")
                
                # 测试web_search工具
                print(f"\n🌐 测试web_search工具...")
                try:
                    result = await session.call_tool(
                        "web_search",
                        arguments={
                            "queries": ["机器学习", "深度学习"],
                            "topic": "人工智能",
                            "top_n": 3
                        }
                    )
                    
                    print(f"  ✅ web_search调用成功")
                    print(f"  📊 返回结果数量: {len(result.content)}")
                    
                    # 解析返回内容
                    if result.content and len(result.content) > 0:
                        content_text = result.content[0].text
                        print(f"  📝 返回内容预览: {content_text[:200]}...")
                        
                        # 尝试解析JSON
                        try:
                            result_data = json.loads(content_text)
                            if isinstance(result_data, list):
                                print(f"  🎯 成功获取 {len(result_data)} 个URL:")
                                for i, url in enumerate(result_data[:3]):
                                    print(f"     {i+1}. {url}")
                            else:
                                print(f"  📋 返回数据类型: {type(result_data)}")
                        except json.JSONDecodeError:
                            print(f"  ⚠️ 返回内容不是有效JSON")
                    else:
                        print(f"  ❌ 返回内容为空")
                        
                except Exception as tool_error:
                    print(f"  ❌ web_search调用失败: {tool_error}")
                    import traceback
                    traceback.print_exc()
                
                print(f"\n✅ MCP测试完成")
                
    except Exception as e:
        print(f"❌ MCP测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

async def main():
    """主函数"""
    success = await test_mcp_web_search()
    if success:
        print("\n🎉 所有测试通过!")
        return 0
    else:
        print("\n💥 测试失败!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
