#!/usr/bin/env python3
"""
测试修复后的对话历史功能
验证工具调用结果能够正确传递给下一个工具
"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.search.analyse import AnalyseInterface

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_conversation_history.log')
    ]
)
logger = logging.getLogger(__name__)

async def test_conversation_history_flow():
    """测试对话历史在工具链中的传递"""
    print("🧪 测试对话历史功能...")
    
    try:
        # 初始化分析接口
        analyser = AnalyseInterface(
            base_dir="new/test_conversation_history",
            max_interaction_rounds=1,
            llm_model="gemini-2.0-flash-thinking-exp-01-21",
            llm_infer_type="OpenAI"
        )
        
        print("✅ AnalyseInterface 初始化成功")
        
        # 测试主题
        topic = "machine learning interpretability"
        description = "Research on explainable AI and model interpretability techniques"
        
        print(f"📝 测试主题: {topic}")
        print(f"📄 描述: {description}")
        
        # 执行分析
        print("\n🔄 开始执行分析...")
        results = await analyser.analyse(
            topic=topic,
            description=description,
            top_n=5
        )
        
        print(f"\n📊 分析完成!")
        print(f"📈 结果数量: {len(results)}")
        
        # 检查对话历史
        print(f"\n💭 对话历史记录数: {len(analyser.conversation_history)}")
        
        # 显示对话历史结构
        for i, msg in enumerate(analyser.conversation_history):
            print(f"  消息 {i+1}: {msg['role']}")
            if msg['role'] == 'assistant' and 'tool_calls' in msg:
                tool_call = msg['tool_calls'][0]
                print(f"    工具调用: {tool_call['function']['name']}")
            elif msg['role'] == 'tool':
                content_preview = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
                print(f"    工具结果: {content_preview}")
        
        # 检查搜索记忆
        print(f"\n🧠 搜索记忆记录数: {len(analyser.search_memory)}")
        for i, memory in enumerate(analyser.search_memory):
            print(f"  步骤 {i+1}: {memory['tool_used']}")
            
        # 验证结果
        if len(results) > 0:
            print("\n✅ 测试成功: 获得了文献结果!")
            print(f"📚 第一篇文献标题: {results[0].get('title', 'N/A')}")
            return True
        else:
            print("\n❌ 测试失败: 没有获得文献结果")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tool_data_flow():
    """专门测试工具间数据流传递"""
    print("\n🔧 测试工具间数据流传递...")
    
    try:
        analyser = AnalyseInterface(
            base_dir="new/test_data_flow",
            max_interaction_rounds=1,
            llm_model="gemini-2.0-flash-thinking-exp-01-21",
            llm_infer_type="OpenAI"
        )
        
        # 手动执行工具链，检查每一步的数据传递
        async with analyser._create_mcp_session() as session:
            # 获取可用工具
            tools_result = await session.list_tools()
            available_tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                }
                for tool in tools_result.tools
            ]
            
            print(f"📋 可用工具数量: {len(available_tools)}")
            
            # 步骤1: 生成查询
            print("\n🔍 步骤1: 生成搜索查询...")
            step1_result = await analyser._call_mcp_tool_with_llm(
                session, 
                "为主题 'machine learning interpretability' 生成搜索查询",
                available_tools
            )
            
            print(f"✅ 步骤1完成，工具: {step1_result['tool_name']}")
            
            # 检查对话历史
            print(f"💭 当前对话历史长度: {len(analyser.conversation_history)}")
            
            # 步骤2: 执行搜索
            print("\n🌐 步骤2: 执行网络搜索...")
            step2_result = await analyser._call_mcp_tool_with_llm(
                session,
                "使用之前生成的查询执行网络搜索",
                available_tools
            )
            
            print(f"✅ 步骤2完成，工具: {step2_result['tool_name']}")
            print(f"💭 当前对话历史长度: {len(analyser.conversation_history)}")
            
            # 步骤3: 爬取URL
            print("\n📄 步骤3: 爬取URL内容...")
            step3_result = await analyser._call_mcp_tool_with_llm(
                session,
                "爬取之前搜索得到的URL内容",
                available_tools
            )
            
            print(f"✅ 步骤3完成，工具: {step3_result['tool_name']}")
            print(f"💭 最终对话历史长度: {len(analyser.conversation_history)}")
            
            # 分析最终结果
            final_result = step3_result['result']
            if hasattr(final_result, 'content') and final_result.content:
                try:
                    result_text = final_result.content[0].text
                    parsed_result = json.loads(result_text)
                    papers = parsed_result.get('papers', [])
                    print(f"📚 最终获得论文数量: {len(papers)}")
                    
                    if len(papers) > 0:
                        print("✅ 数据流测试成功!")
                        return True
                    else:
                        print("❌ 数据流测试失败: 没有获得论文")
                        return False
                except Exception as e:
                    print(f"❌ 解析最终结果失败: {e}")
                    return False
            else:
                print("❌ 最终结果为空")
                return False
                
    except Exception as e:
        print(f"❌ 数据流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试对话历史修复...")
    
    # 测试1: 完整工作流
    test1_success = await test_conversation_history_flow()
    
    # 测试2: 工具数据流
    test2_success = await test_tool_data_flow()
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试总结:")
    print(f"  完整工作流测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  工具数据流测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过! 对话历史功能修复成功!")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
